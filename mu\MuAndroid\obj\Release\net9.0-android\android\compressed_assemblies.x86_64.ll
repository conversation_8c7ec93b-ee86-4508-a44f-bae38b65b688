; ModuleID = 'compressed_assemblies.x86_64.ll'
source_filename = "compressed_assemblies.x86_64.ll"
target datalayout = "e-m:e-p270:32:32-p271:32:32-p272:64:64-i64:64-f80:128-n8:16:32:64-S128"
target triple = "x86_64-unknown-linux-android21"

%struct.CompressedAssemblies = type {
	i32, ; uint32_t count
	ptr ; CompressedAssemblyDescriptor descriptors
}

%struct.CompressedAssemblyDescriptor = type {
	i32, ; uint32_t uncompressed_file_size
	i1, ; bool loaded
	ptr ; uint8_t data
}

@compressed_assemblies = dso_local local_unnamed_addr global %struct.CompressedAssemblies {
	i32 239, ; uint32_t count
	ptr @compressed_assembly_descriptors; CompressedAssemblyDescriptor* descriptors
}, align 8

@compressed_assembly_descriptors = internal dso_local global [239 x %struct.CompressedAssemblyDescriptor] [
	%struct.CompressedAssemblyDescriptor {
		i32 8192, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_0; uint8_t* data
	}, ; 0: MuAndroid
	%struct.CompressedAssemblyDescriptor {
		i32 264192, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_1; uint8_t* data
	}, ; 1: BCnEncoder
	%struct.CompressedAssemblyDescriptor {
		i32 3346432, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_2; uint8_t* data
	}, ; 2: BouncyCastle.Crypto
	%struct.CompressedAssemblyDescriptor {
		i32 123176, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_3; uint8_t* data
	}, ; 3: CommunityToolkit.HighPerformance
	%struct.CompressedAssemblyDescriptor {
		i32 46080, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_4; uint8_t* data
	}, ; 4: Delizious.Ini
	%struct.CompressedAssemblyDescriptor {
		i32 30208, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_5; uint8_t* data
	}, ; 5: INIFileParser
	%struct.CompressedAssemblyDescriptor {
		i32 38400, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_6; uint8_t* data
	}, ; 6: LEA.NET
	%struct.CompressedAssemblyDescriptor {
		i32 2938120, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_7; uint8_t* data
	}, ; 7: Microsoft.CodeAnalysis
	%struct.CompressedAssemblyDescriptor {
		i32 6412448, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_8; uint8_t* data
	}, ; 8: Microsoft.CodeAnalysis.CSharp
	%struct.CompressedAssemblyDescriptor {
		i32 45352, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_9; uint8_t* data
	}, ; 9: Microsoft.Extensions.Configuration
	%struct.CompressedAssemblyDescriptor {
		i32 29480, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_10; uint8_t* data
	}, ; 10: Microsoft.Extensions.Configuration.Abstractions
	%struct.CompressedAssemblyDescriptor {
		i32 44840, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_11; uint8_t* data
	}, ; 11: Microsoft.Extensions.Configuration.Binder
	%struct.CompressedAssemblyDescriptor {
		i32 29480, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_12; uint8_t* data
	}, ; 12: Microsoft.Extensions.Configuration.FileExtensions
	%struct.CompressedAssemblyDescriptor {
		i32 28456, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_13; uint8_t* data
	}, ; 13: Microsoft.Extensions.Configuration.Json
	%struct.CompressedAssemblyDescriptor {
		i32 93480, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_14; uint8_t* data
	}, ; 14: Microsoft.Extensions.DependencyInjection
	%struct.CompressedAssemblyDescriptor {
		i32 65832, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_15; uint8_t* data
	}, ; 15: Microsoft.Extensions.DependencyInjection.Abstractions
	%struct.CompressedAssemblyDescriptor {
		i32 23824, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_16; uint8_t* data
	}, ; 16: Microsoft.Extensions.FileProviders.Abstractions
	%struct.CompressedAssemblyDescriptor {
		i32 46344, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_17; uint8_t* data
	}, ; 17: Microsoft.Extensions.FileProviders.Physical
	%struct.CompressedAssemblyDescriptor {
		i32 46856, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_18; uint8_t* data
	}, ; 18: Microsoft.Extensions.FileSystemGlobbing
	%struct.CompressedAssemblyDescriptor {
		i32 52520, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_19; uint8_t* data
	}, ; 19: Microsoft.Extensions.Logging
	%struct.CompressedAssemblyDescriptor {
		i32 67344, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_20; uint8_t* data
	}, ; 20: Microsoft.Extensions.Logging.Abstractions
	%struct.CompressedAssemblyDescriptor {
		i32 29480, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_21; uint8_t* data
	}, ; 21: Microsoft.Extensions.Logging.Configuration
	%struct.CompressedAssemblyDescriptor {
		i32 75560, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_22; uint8_t* data
	}, ; 22: Microsoft.Extensions.Logging.Console
	%struct.CompressedAssemblyDescriptor {
		i32 66344, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_23; uint8_t* data
	}, ; 23: Microsoft.Extensions.Options
	%struct.CompressedAssemblyDescriptor {
		i32 22824, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_24; uint8_t* data
	}, ; 24: Microsoft.Extensions.Options.ConfigurationExtensions
	%struct.CompressedAssemblyDescriptor {
		i32 45352, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_25; uint8_t* data
	}, ; 25: Microsoft.Extensions.Primitives
	%struct.CompressedAssemblyDescriptor {
		i32 1001472, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_26; uint8_t* data
	}, ; 26: MonoGame.Framework
	%struct.CompressedAssemblyDescriptor {
		i32 363008, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_27; uint8_t* data
	}, ; 27: MUnique.OpenMU.Network
	%struct.CompressedAssemblyDescriptor {
		i32 1423360, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_28; uint8_t* data
	}, ; 28: MUnique.OpenMU.Network.Packets
	%struct.CompressedAssemblyDescriptor {
		i32 46592, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_29; uint8_t* data
	}, ; 29: MUnique.OpenMU.PlugIns
	%struct.CompressedAssemblyDescriptor {
		i32 40960, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_30; uint8_t* data
	}, ; 30: Nito.AsyncEx.Coordination
	%struct.CompressedAssemblyDescriptor {
		i32 35840, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_31; uint8_t* data
	}, ; 31: Nito.AsyncEx.Tasks
	%struct.CompressedAssemblyDescriptor {
		i32 16896, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_32; uint8_t* data
	}, ; 32: Nito.Collections.Deque
	%struct.CompressedAssemblyDescriptor {
		i32 22528, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_33; uint8_t* data
	}, ; 33: Nito.Disposables
	%struct.CompressedAssemblyDescriptor {
		i32 72192, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_34; uint8_t* data
	}, ; 34: NLayer
	%struct.CompressedAssemblyDescriptor {
		i32 169984, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_35; uint8_t* data
	}, ; 35: Pipelines.Sockets.Unofficial
	%struct.CompressedAssemblyDescriptor {
		i32 2092544, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_36; uint8_t* data
	}, ; 36: SixLabors.ImageSharp
	%struct.CompressedAssemblyDescriptor {
		i32 47368, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_37; uint8_t* data
	}, ; 37: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 49312, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_38; uint8_t* data
	}, ; 38: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 48816, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_39; uint8_t* data
	}, ; 39: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 49824, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_40; uint8_t* data
	}, ; 40: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 49840, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_41; uint8_t* data
	}, ; 41: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 52912, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_42; uint8_t* data
	}, ; 42: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 49824, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_43; uint8_t* data
	}, ; 43: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 49824, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_44; uint8_t* data
	}, ; 44: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 48288, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_45; uint8_t* data
	}, ; 45: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 59552, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_46; uint8_t* data
	}, ; 46: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 47776, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_47; uint8_t* data
	}, ; 47: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 44720, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_48; uint8_t* data
	}, ; 48: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 44704, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_49; uint8_t* data
	}, ; 49: Microsoft.CodeAnalysis.resources
	%struct.CompressedAssemblyDescriptor {
		i32 423072, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_50; uint8_t* data
	}, ; 50: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 452256, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_51; uint8_t* data
	}, ; 51: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 443040, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_52; uint8_t* data
	}, ; 52: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 453792, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_53; uint8_t* data
	}, ; 53: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 449184, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_54; uint8_t* data
	}, ; 54: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 495392, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_55; uint8_t* data
	}, ; 55: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 454408, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_56; uint8_t* data
	}, ; 56: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 455328, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_57; uint8_t* data
	}, ; 57: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 434848, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_58; uint8_t* data
	}, ; 58: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 598704, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_59; uint8_t* data
	}, ; 59: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 431264, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_60; uint8_t* data
	}, ; 60: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 383648, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_61; uint8_t* data
	}, ; 61: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 383136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_62; uint8_t* data
	}, ; 62: Microsoft.CodeAnalysis.CSharp.resources
	%struct.CompressedAssemblyDescriptor {
		i32 49664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_63; uint8_t* data
	}, ; 63: Client.Data
	%struct.CompressedAssemblyDescriptor {
		i32 1320448, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_64; uint8_t* data
	}, ; 64: Client.Main
	%struct.CompressedAssemblyDescriptor {
		i32 3072, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_65; uint8_t* data
	}, ; 65: _Microsoft.Android.Resource.Designer
	%struct.CompressedAssemblyDescriptor {
		i32 308024, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_66; uint8_t* data
	}, ; 66: Microsoft.CSharp
	%struct.CompressedAssemblyDescriptor {
		i32 430360, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_67; uint8_t* data
	}, ; 67: Microsoft.VisualBasic.Core
	%struct.CompressedAssemblyDescriptor {
		i32 17704, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_68; uint8_t* data
	}, ; 68: Microsoft.VisualBasic
	%struct.CompressedAssemblyDescriptor {
		i32 15624, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_69; uint8_t* data
	}, ; 69: Microsoft.Win32.Primitives
	%struct.CompressedAssemblyDescriptor {
		i32 33560, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_70; uint8_t* data
	}, ; 70: Microsoft.Win32.Registry
	%struct.CompressedAssemblyDescriptor {
		i32 15624, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_71; uint8_t* data
	}, ; 71: System.AppContext
	%struct.CompressedAssemblyDescriptor {
		i32 15664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_72; uint8_t* data
	}, ; 72: System.Buffers
	%struct.CompressedAssemblyDescriptor {
		i32 89912, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_73; uint8_t* data
	}, ; 73: System.Collections.Concurrent
	%struct.CompressedAssemblyDescriptor {
		i32 255792, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_74; uint8_t* data
	}, ; 74: System.Collections.Immutable
	%struct.CompressedAssemblyDescriptor {
		i32 48400, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_75; uint8_t* data
	}, ; 75: System.Collections.NonGeneric
	%struct.CompressedAssemblyDescriptor {
		i32 48392, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_76; uint8_t* data
	}, ; 76: System.Collections.Specialized
	%struct.CompressedAssemblyDescriptor {
		i32 126728, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_77; uint8_t* data
	}, ; 77: System.Collections
	%struct.CompressedAssemblyDescriptor {
		i32 102688, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_78; uint8_t* data
	}, ; 78: System.ComponentModel.Annotations
	%struct.CompressedAssemblyDescriptor {
		i32 17160, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_79; uint8_t* data
	}, ; 79: System.ComponentModel.DataAnnotations
	%struct.CompressedAssemblyDescriptor {
		i32 26888, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_80; uint8_t* data
	}, ; 80: System.ComponentModel.EventBasedAsync
	%struct.CompressedAssemblyDescriptor {
		i32 42264, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_81; uint8_t* data
	}, ; 81: System.ComponentModel.Primitives
	%struct.CompressedAssemblyDescriptor {
		i32 315688, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_82; uint8_t* data
	}, ; 82: System.ComponentModel.TypeConverter
	%struct.CompressedAssemblyDescriptor {
		i32 16664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_83; uint8_t* data
	}, ; 83: System.ComponentModel
	%struct.CompressedAssemblyDescriptor {
		i32 19720, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_84; uint8_t* data
	}, ; 84: System.Configuration
	%struct.CompressedAssemblyDescriptor {
		i32 51000, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_85; uint8_t* data
	}, ; 85: System.Console
	%struct.CompressedAssemblyDescriptor {
		i32 23840, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_86; uint8_t* data
	}, ; 86: System.Core
	%struct.CompressedAssemblyDescriptor {
		i32 1016616, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_87; uint8_t* data
	}, ; 87: System.Data.Common
	%struct.CompressedAssemblyDescriptor {
		i32 16168, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_88; uint8_t* data
	}, ; 88: System.Data.DataSetExtensions
	%struct.CompressedAssemblyDescriptor {
		i32 25400, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_89; uint8_t* data
	}, ; 89: System.Data
	%struct.CompressedAssemblyDescriptor {
		i32 16680, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_90; uint8_t* data
	}, ; 90: System.Diagnostics.Contracts
	%struct.CompressedAssemblyDescriptor {
		i32 16168, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_91; uint8_t* data
	}, ; 91: System.Diagnostics.Debug
	%struct.CompressedAssemblyDescriptor {
		i32 184600, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_92; uint8_t* data
	}, ; 92: System.Diagnostics.DiagnosticSource
	%struct.CompressedAssemblyDescriptor {
		i32 29496, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_93; uint8_t* data
	}, ; 93: System.Diagnostics.FileVersionInfo
	%struct.CompressedAssemblyDescriptor {
		i32 127256, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_94; uint8_t* data
	}, ; 94: System.Diagnostics.Process
	%struct.CompressedAssemblyDescriptor {
		i32 26384, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_95; uint8_t* data
	}, ; 95: System.Diagnostics.StackTrace
	%struct.CompressedAssemblyDescriptor {
		i32 32048, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_96; uint8_t* data
	}, ; 96: System.Diagnostics.TextWriterTraceListener
	%struct.CompressedAssemblyDescriptor {
		i32 15632, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_97; uint8_t* data
	}, ; 97: System.Diagnostics.Tools
	%struct.CompressedAssemblyDescriptor {
		i32 59176, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_98; uint8_t* data
	}, ; 98: System.Diagnostics.TraceSource
	%struct.CompressedAssemblyDescriptor {
		i32 16680, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_99; uint8_t* data
	}, ; 99: System.Diagnostics.Tracing
	%struct.CompressedAssemblyDescriptor {
		i32 64816, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_100; uint8_t* data
	}, ; 100: System.Drawing.Primitives
	%struct.CompressedAssemblyDescriptor {
		i32 20744, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_101; uint8_t* data
	}, ; 101: System.Drawing
	%struct.CompressedAssemblyDescriptor {
		i32 16680, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_102; uint8_t* data
	}, ; 102: System.Dynamic.Runtime
	%struct.CompressedAssemblyDescriptor {
		i32 96536, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_103; uint8_t* data
	}, ; 103: System.Formats.Asn1
	%struct.CompressedAssemblyDescriptor {
		i32 121608, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_104; uint8_t* data
	}, ; 104: System.Formats.Tar
	%struct.CompressedAssemblyDescriptor {
		i32 16152, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_105; uint8_t* data
	}, ; 105: System.Globalization.Calendars
	%struct.CompressedAssemblyDescriptor {
		i32 15664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_106; uint8_t* data
	}, ; 106: System.Globalization.Extensions
	%struct.CompressedAssemblyDescriptor {
		i32 16136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_107; uint8_t* data
	}, ; 107: System.Globalization
	%struct.CompressedAssemblyDescriptor {
		i32 41224, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_108; uint8_t* data
	}, ; 108: System.IO.Compression.Brotli
	%struct.CompressedAssemblyDescriptor {
		i32 15664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_109; uint8_t* data
	}, ; 109: System.IO.Compression.FileSystem
	%struct.CompressedAssemblyDescriptor {
		i32 38184, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_110; uint8_t* data
	}, ; 110: System.IO.Compression.ZipFile
	%struct.CompressedAssemblyDescriptor {
		i32 110344, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_111; uint8_t* data
	}, ; 111: System.IO.Compression
	%struct.CompressedAssemblyDescriptor {
		i32 32560, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_112; uint8_t* data
	}, ; 112: System.IO.FileSystem.AccessControl
	%struct.CompressedAssemblyDescriptor {
		i32 48440, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_113; uint8_t* data
	}, ; 113: System.IO.FileSystem.DriveInfo
	%struct.CompressedAssemblyDescriptor {
		i32 15624, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_114; uint8_t* data
	}, ; 114: System.IO.FileSystem.Primitives
	%struct.CompressedAssemblyDescriptor {
		i32 55072, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_115; uint8_t* data
	}, ; 115: System.IO.FileSystem.Watcher
	%struct.CompressedAssemblyDescriptor {
		i32 16176, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_116; uint8_t* data
	}, ; 116: System.IO.FileSystem
	%struct.CompressedAssemblyDescriptor {
		i32 43800, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_117; uint8_t* data
	}, ; 117: System.IO.IsolatedStorage
	%struct.CompressedAssemblyDescriptor {
		i32 48920, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_118; uint8_t* data
	}, ; 118: System.IO.MemoryMappedFiles
	%struct.CompressedAssemblyDescriptor {
		i32 78640, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_119; uint8_t* data
	}, ; 119: System.IO.Pipelines
	%struct.CompressedAssemblyDescriptor {
		i32 23816, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_120; uint8_t* data
	}, ; 120: System.IO.Pipes.AccessControl
	%struct.CompressedAssemblyDescriptor {
		i32 67888, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_121; uint8_t* data
	}, ; 121: System.IO.Pipes
	%struct.CompressedAssemblyDescriptor {
		i32 15640, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_122; uint8_t* data
	}, ; 122: System.IO.UnmanagedMemoryStream
	%struct.CompressedAssemblyDescriptor {
		i32 16184, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_123; uint8_t* data
	}, ; 123: System.IO
	%struct.CompressedAssemblyDescriptor {
		i32 575768, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_124; uint8_t* data
	}, ; 124: System.Linq.Expressions
	%struct.CompressedAssemblyDescriptor {
		i32 223528, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_125; uint8_t* data
	}, ; 125: System.Linq.Parallel
	%struct.CompressedAssemblyDescriptor {
		i32 76576, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_126; uint8_t* data
	}, ; 126: System.Linq.Queryable
	%struct.CompressedAssemblyDescriptor {
		i32 149288, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_127; uint8_t* data
	}, ; 127: System.Linq
	%struct.CompressedAssemblyDescriptor {
		i32 56088, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_128; uint8_t* data
	}, ; 128: System.Memory
	%struct.CompressedAssemblyDescriptor {
		i32 56600, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_129; uint8_t* data
	}, ; 129: System.Net.Http.Json
	%struct.CompressedAssemblyDescriptor {
		i32 676664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_130; uint8_t* data
	}, ; 130: System.Net.Http
	%struct.CompressedAssemblyDescriptor {
		i32 131856, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_131; uint8_t* data
	}, ; 131: System.Net.HttpListener
	%struct.CompressedAssemblyDescriptor {
		i32 174864, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_132; uint8_t* data
	}, ; 132: System.Net.Mail
	%struct.CompressedAssemblyDescriptor {
		i32 52016, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_133; uint8_t* data
	}, ; 133: System.Net.NameResolution
	%struct.CompressedAssemblyDescriptor {
		i32 66352, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_134; uint8_t* data
	}, ; 134: System.Net.NetworkInformation
	%struct.CompressedAssemblyDescriptor {
		i32 56088, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_135; uint8_t* data
	}, ; 135: System.Net.Ping
	%struct.CompressedAssemblyDescriptor {
		i32 107320, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_136; uint8_t* data
	}, ; 136: System.Net.Primitives
	%struct.CompressedAssemblyDescriptor {
		i32 173352, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_137; uint8_t* data
	}, ; 137: System.Net.Quic
	%struct.CompressedAssemblyDescriptor {
		i32 162056, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_138; uint8_t* data
	}, ; 138: System.Net.Requests
	%struct.CompressedAssemblyDescriptor {
		i32 253728, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_139; uint8_t* data
	}, ; 139: System.Net.Security
	%struct.CompressedAssemblyDescriptor {
		i32 15648, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_140; uint8_t* data
	}, ; 140: System.Net.ServicePoint
	%struct.CompressedAssemblyDescriptor {
		i32 235296, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_141; uint8_t* data
	}, ; 141: System.Net.Sockets
	%struct.CompressedAssemblyDescriptor {
		i32 70936, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_142; uint8_t* data
	}, ; 142: System.Net.WebClient
	%struct.CompressedAssemblyDescriptor {
		i32 33576, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_143; uint8_t* data
	}, ; 143: System.Net.WebHeaderCollection
	%struct.CompressedAssemblyDescriptor {
		i32 23832, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_144; uint8_t* data
	}, ; 144: System.Net.WebProxy
	%struct.CompressedAssemblyDescriptor {
		i32 51984, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_145; uint8_t* data
	}, ; 145: System.Net.WebSockets.Client
	%struct.CompressedAssemblyDescriptor {
		i32 103184, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_146; uint8_t* data
	}, ; 146: System.Net.WebSockets
	%struct.CompressedAssemblyDescriptor {
		i32 17688, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_147; uint8_t* data
	}, ; 147: System.Net
	%struct.CompressedAssemblyDescriptor {
		i32 16176, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_148; uint8_t* data
	}, ; 148: System.Numerics.Vectors
	%struct.CompressedAssemblyDescriptor {
		i32 15672, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_149; uint8_t* data
	}, ; 149: System.Numerics
	%struct.CompressedAssemblyDescriptor {
		i32 41736, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_150; uint8_t* data
	}, ; 150: System.ObjectModel
	%struct.CompressedAssemblyDescriptor {
		i32 852280, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_151; uint8_t* data
	}, ; 151: System.Private.DataContractSerialization
	%struct.CompressedAssemblyDescriptor {
		i32 103216, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_152; uint8_t* data
	}, ; 152: System.Private.Uri
	%struct.CompressedAssemblyDescriptor {
		i32 153880, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_153; uint8_t* data
	}, ; 153: System.Private.Xml.Linq
	%struct.CompressedAssemblyDescriptor {
		i32 3099936, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_154; uint8_t* data
	}, ; 154: System.Private.Xml
	%struct.CompressedAssemblyDescriptor {
		i32 38704, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_155; uint8_t* data
	}, ; 155: System.Reflection.DispatchProxy
	%struct.CompressedAssemblyDescriptor {
		i32 16176, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_156; uint8_t* data
	}, ; 156: System.Reflection.Emit.ILGeneration
	%struct.CompressedAssemblyDescriptor {
		i32 16136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_157; uint8_t* data
	}, ; 157: System.Reflection.Emit.Lightweight
	%struct.CompressedAssemblyDescriptor {
		i32 130344, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_158; uint8_t* data
	}, ; 158: System.Reflection.Emit
	%struct.CompressedAssemblyDescriptor {
		i32 15664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_159; uint8_t* data
	}, ; 159: System.Reflection.Extensions
	%struct.CompressedAssemblyDescriptor {
		i32 501512, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_160; uint8_t* data
	}, ; 160: System.Reflection.Metadata
	%struct.CompressedAssemblyDescriptor {
		i32 16136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_161; uint8_t* data
	}, ; 161: System.Reflection.Primitives
	%struct.CompressedAssemblyDescriptor {
		i32 24360, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_162; uint8_t* data
	}, ; 162: System.Reflection.TypeExtensions
	%struct.CompressedAssemblyDescriptor {
		i32 16648, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_163; uint8_t* data
	}, ; 163: System.Reflection
	%struct.CompressedAssemblyDescriptor {
		i32 15648, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_164; uint8_t* data
	}, ; 164: System.Resources.Reader
	%struct.CompressedAssemblyDescriptor {
		i32 16184, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_165; uint8_t* data
	}, ; 165: System.Resources.ResourceManager
	%struct.CompressedAssemblyDescriptor {
		i32 26888, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_166; uint8_t* data
	}, ; 166: System.Resources.Writer
	%struct.CompressedAssemblyDescriptor {
		i32 15664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_167; uint8_t* data
	}, ; 167: System.Runtime.CompilerServices.Unsafe
	%struct.CompressedAssemblyDescriptor {
		i32 17672, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_168; uint8_t* data
	}, ; 168: System.Runtime.CompilerServices.VisualC
	%struct.CompressedAssemblyDescriptor {
		i32 18216, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_169; uint8_t* data
	}, ; 169: System.Runtime.Extensions
	%struct.CompressedAssemblyDescriptor {
		i32 15664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_170; uint8_t* data
	}, ; 170: System.Runtime.Handles
	%struct.CompressedAssemblyDescriptor {
		i32 38672, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_171; uint8_t* data
	}, ; 171: System.Runtime.InteropServices.JavaScript
	%struct.CompressedAssemblyDescriptor {
		i32 15656, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_172; uint8_t* data
	}, ; 172: System.Runtime.InteropServices.RuntimeInformation
	%struct.CompressedAssemblyDescriptor {
		i32 64784, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_173; uint8_t* data
	}, ; 173: System.Runtime.InteropServices
	%struct.CompressedAssemblyDescriptor {
		i32 17704, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_174; uint8_t* data
	}, ; 174: System.Runtime.Intrinsics
	%struct.CompressedAssemblyDescriptor {
		i32 16136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_175; uint8_t* data
	}, ; 175: System.Runtime.Loader
	%struct.CompressedAssemblyDescriptor {
		i32 143632, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_176; uint8_t* data
	}, ; 176: System.Runtime.Numerics
	%struct.CompressedAssemblyDescriptor {
		i32 66352, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_177; uint8_t* data
	}, ; 177: System.Runtime.Serialization.Formatters
	%struct.CompressedAssemblyDescriptor {
		i32 16168, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_178; uint8_t* data
	}, ; 178: System.Runtime.Serialization.Json
	%struct.CompressedAssemblyDescriptor {
		i32 23856, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_179; uint8_t* data
	}, ; 179: System.Runtime.Serialization.Primitives
	%struct.CompressedAssemblyDescriptor {
		i32 17208, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_180; uint8_t* data
	}, ; 180: System.Runtime.Serialization.Xml
	%struct.CompressedAssemblyDescriptor {
		i32 17200, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_181; uint8_t* data
	}, ; 181: System.Runtime.Serialization
	%struct.CompressedAssemblyDescriptor {
		i32 44840, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_182; uint8_t* data
	}, ; 182: System.Runtime
	%struct.CompressedAssemblyDescriptor {
		i32 58664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_183; uint8_t* data
	}, ; 183: System.Security.AccessControl
	%struct.CompressedAssemblyDescriptor {
		i32 54040, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_184; uint8_t* data
	}, ; 184: System.Security.Claims
	%struct.CompressedAssemblyDescriptor {
		i32 17688, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_185; uint8_t* data
	}, ; 185: System.Security.Cryptography.Algorithms
	%struct.CompressedAssemblyDescriptor {
		i32 16664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_186; uint8_t* data
	}, ; 186: System.Security.Cryptography.Cng
	%struct.CompressedAssemblyDescriptor {
		i32 16184, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_187; uint8_t* data
	}, ; 187: System.Security.Cryptography.Csp
	%struct.CompressedAssemblyDescriptor {
		i32 16136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_188; uint8_t* data
	}, ; 188: System.Security.Cryptography.Encoding
	%struct.CompressedAssemblyDescriptor {
		i32 15624, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_189; uint8_t* data
	}, ; 189: System.Security.Cryptography.OpenSsl
	%struct.CompressedAssemblyDescriptor {
		i32 16136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_190; uint8_t* data
	}, ; 190: System.Security.Cryptography.Primitives
	%struct.CompressedAssemblyDescriptor {
		i32 17200, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_191; uint8_t* data
	}, ; 191: System.Security.Cryptography.X509Certificates
	%struct.CompressedAssemblyDescriptor {
		i32 705336, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_192; uint8_t* data
	}, ; 192: System.Security.Cryptography
	%struct.CompressedAssemblyDescriptor {
		i32 38176, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_193; uint8_t* data
	}, ; 193: System.Security.Principal.Windows
	%struct.CompressedAssemblyDescriptor {
		i32 15664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_194; uint8_t* data
	}, ; 194: System.Security.Principal
	%struct.CompressedAssemblyDescriptor {
		i32 15664, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_195; uint8_t* data
	}, ; 195: System.Security.SecureString
	%struct.CompressedAssemblyDescriptor {
		i32 18736, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_196; uint8_t* data
	}, ; 196: System.Security
	%struct.CompressedAssemblyDescriptor {
		i32 17200, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_197; uint8_t* data
	}, ; 197: System.ServiceModel.Web
	%struct.CompressedAssemblyDescriptor {
		i32 16136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_198; uint8_t* data
	}, ; 198: System.ServiceProcess
	%struct.CompressedAssemblyDescriptor {
		i32 741168, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_199; uint8_t* data
	}, ; 199: System.Text.Encoding.CodePages
	%struct.CompressedAssemblyDescriptor {
		i32 16136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_200; uint8_t* data
	}, ; 200: System.Text.Encoding.Extensions
	%struct.CompressedAssemblyDescriptor {
		i32 16136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_201; uint8_t* data
	}, ; 201: System.Text.Encoding
	%struct.CompressedAssemblyDescriptor {
		i32 70408, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_202; uint8_t* data
	}, ; 202: System.Text.Encodings.Web
	%struct.CompressedAssemblyDescriptor {
		i32 617776, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_203; uint8_t* data
	}, ; 203: System.Text.Json
	%struct.CompressedAssemblyDescriptor {
		i32 369432, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_204; uint8_t* data
	}, ; 204: System.Text.RegularExpressions
	%struct.CompressedAssemblyDescriptor {
		i32 57096, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_205; uint8_t* data
	}, ; 205: System.Threading.Channels
	%struct.CompressedAssemblyDescriptor {
		i32 16184, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_206; uint8_t* data
	}, ; 206: System.Threading.Overlapped
	%struct.CompressedAssemblyDescriptor {
		i32 186136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_207; uint8_t* data
	}, ; 207: System.Threading.Tasks.Dataflow
	%struct.CompressedAssemblyDescriptor {
		i32 16176, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_208; uint8_t* data
	}, ; 208: System.Threading.Tasks.Extensions
	%struct.CompressedAssemblyDescriptor {
		i32 61704, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_209; uint8_t* data
	}, ; 209: System.Threading.Tasks.Parallel
	%struct.CompressedAssemblyDescriptor {
		i32 17200, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_210; uint8_t* data
	}, ; 210: System.Threading.Tasks
	%struct.CompressedAssemblyDescriptor {
		i32 16160, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_211; uint8_t* data
	}, ; 211: System.Threading.Thread
	%struct.CompressedAssemblyDescriptor {
		i32 16176, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_212; uint8_t* data
	}, ; 212: System.Threading.ThreadPool
	%struct.CompressedAssemblyDescriptor {
		i32 15656, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_213; uint8_t* data
	}, ; 213: System.Threading.Timer
	%struct.CompressedAssemblyDescriptor {
		i32 45328, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_214; uint8_t* data
	}, ; 214: System.Threading
	%struct.CompressedAssemblyDescriptor {
		i32 175912, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_215; uint8_t* data
	}, ; 215: System.Transactions.Local
	%struct.CompressedAssemblyDescriptor {
		i32 16672, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_216; uint8_t* data
	}, ; 216: System.Transactions
	%struct.CompressedAssemblyDescriptor {
		i32 15656, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_217; uint8_t* data
	}, ; 217: System.ValueTuple
	%struct.CompressedAssemblyDescriptor {
		i32 30520, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_218; uint8_t* data
	}, ; 218: System.Web.HttpUtility
	%struct.CompressedAssemblyDescriptor {
		i32 15656, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_219; uint8_t* data
	}, ; 219: System.Web
	%struct.CompressedAssemblyDescriptor {
		i32 16168, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_220; uint8_t* data
	}, ; 220: System.Windows
	%struct.CompressedAssemblyDescriptor {
		i32 16136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_221; uint8_t* data
	}, ; 221: System.Xml.Linq
	%struct.CompressedAssemblyDescriptor {
		i32 22320, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_222; uint8_t* data
	}, ; 222: System.Xml.ReaderWriter
	%struct.CompressedAssemblyDescriptor {
		i32 16680, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_223; uint8_t* data
	}, ; 223: System.Xml.Serialization
	%struct.CompressedAssemblyDescriptor {
		i32 16184, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_224; uint8_t* data
	}, ; 224: System.Xml.XDocument
	%struct.CompressedAssemblyDescriptor {
		i32 16184, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_225; uint8_t* data
	}, ; 225: System.Xml.XPath.XDocument
	%struct.CompressedAssemblyDescriptor {
		i32 16184, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_226; uint8_t* data
	}, ; 226: System.Xml.XPath
	%struct.CompressedAssemblyDescriptor {
		i32 16136, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_227; uint8_t* data
	}, ; 227: System.Xml.XmlDocument
	%struct.CompressedAssemblyDescriptor {
		i32 18216, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_228; uint8_t* data
	}, ; 228: System.Xml.XmlSerializer
	%struct.CompressedAssemblyDescriptor {
		i32 23848, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_229; uint8_t* data
	}, ; 229: System.Xml
	%struct.CompressedAssemblyDescriptor {
		i32 50984, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_230; uint8_t* data
	}, ; 230: System
	%struct.CompressedAssemblyDescriptor {
		i32 16680, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_231; uint8_t* data
	}, ; 231: WindowsBase
	%struct.CompressedAssemblyDescriptor {
		i32 60192, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_232; uint8_t* data
	}, ; 232: mscorlib
	%struct.CompressedAssemblyDescriptor {
		i32 101160, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_233; uint8_t* data
	}, ; 233: netstandard
	%struct.CompressedAssemblyDescriptor {
		i32 240176, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_234; uint8_t* data
	}, ; 234: Java.Interop
	%struct.CompressedAssemblyDescriptor {
		i32 82976, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_235; uint8_t* data
	}, ; 235: Mono.Android.Export
	%struct.CompressedAssemblyDescriptor {
		i32 18976, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_236; uint8_t* data
	}, ; 236: Mono.Android.Runtime
	%struct.CompressedAssemblyDescriptor {
		i32 37449272, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_237; uint8_t* data
	}, ; 237: Mono.Android
	%struct.CompressedAssemblyDescriptor {
		i32 4776744, ; uint32_t uncompressed_file_size
		i1 false, ; bool loaded
		ptr @__compressedAssemblyData_238; uint8_t* data
	} ; 238: System.Private.CoreLib
], align 16

@__compressedAssemblyData_0 = internal dso_local global [8192 x i8] zeroinitializer, align 16
@__compressedAssemblyData_1 = internal dso_local global [264192 x i8] zeroinitializer, align 16
@__compressedAssemblyData_2 = internal dso_local global [3346432 x i8] zeroinitializer, align 16
@__compressedAssemblyData_3 = internal dso_local global [123176 x i8] zeroinitializer, align 16
@__compressedAssemblyData_4 = internal dso_local global [46080 x i8] zeroinitializer, align 16
@__compressedAssemblyData_5 = internal dso_local global [30208 x i8] zeroinitializer, align 16
@__compressedAssemblyData_6 = internal dso_local global [38400 x i8] zeroinitializer, align 16
@__compressedAssemblyData_7 = internal dso_local global [2938120 x i8] zeroinitializer, align 16
@__compressedAssemblyData_8 = internal dso_local global [6412448 x i8] zeroinitializer, align 16
@__compressedAssemblyData_9 = internal dso_local global [45352 x i8] zeroinitializer, align 16
@__compressedAssemblyData_10 = internal dso_local global [29480 x i8] zeroinitializer, align 16
@__compressedAssemblyData_11 = internal dso_local global [44840 x i8] zeroinitializer, align 16
@__compressedAssemblyData_12 = internal dso_local global [29480 x i8] zeroinitializer, align 16
@__compressedAssemblyData_13 = internal dso_local global [28456 x i8] zeroinitializer, align 16
@__compressedAssemblyData_14 = internal dso_local global [93480 x i8] zeroinitializer, align 16
@__compressedAssemblyData_15 = internal dso_local global [65832 x i8] zeroinitializer, align 16
@__compressedAssemblyData_16 = internal dso_local global [23824 x i8] zeroinitializer, align 16
@__compressedAssemblyData_17 = internal dso_local global [46344 x i8] zeroinitializer, align 16
@__compressedAssemblyData_18 = internal dso_local global [46856 x i8] zeroinitializer, align 16
@__compressedAssemblyData_19 = internal dso_local global [52520 x i8] zeroinitializer, align 16
@__compressedAssemblyData_20 = internal dso_local global [67344 x i8] zeroinitializer, align 16
@__compressedAssemblyData_21 = internal dso_local global [29480 x i8] zeroinitializer, align 16
@__compressedAssemblyData_22 = internal dso_local global [75560 x i8] zeroinitializer, align 16
@__compressedAssemblyData_23 = internal dso_local global [66344 x i8] zeroinitializer, align 16
@__compressedAssemblyData_24 = internal dso_local global [22824 x i8] zeroinitializer, align 16
@__compressedAssemblyData_25 = internal dso_local global [45352 x i8] zeroinitializer, align 16
@__compressedAssemblyData_26 = internal dso_local global [1001472 x i8] zeroinitializer, align 16
@__compressedAssemblyData_27 = internal dso_local global [363008 x i8] zeroinitializer, align 16
@__compressedAssemblyData_28 = internal dso_local global [1423360 x i8] zeroinitializer, align 16
@__compressedAssemblyData_29 = internal dso_local global [46592 x i8] zeroinitializer, align 16
@__compressedAssemblyData_30 = internal dso_local global [40960 x i8] zeroinitializer, align 16
@__compressedAssemblyData_31 = internal dso_local global [35840 x i8] zeroinitializer, align 16
@__compressedAssemblyData_32 = internal dso_local global [16896 x i8] zeroinitializer, align 16
@__compressedAssemblyData_33 = internal dso_local global [22528 x i8] zeroinitializer, align 16
@__compressedAssemblyData_34 = internal dso_local global [72192 x i8] zeroinitializer, align 16
@__compressedAssemblyData_35 = internal dso_local global [169984 x i8] zeroinitializer, align 16
@__compressedAssemblyData_36 = internal dso_local global [2092544 x i8] zeroinitializer, align 16
@__compressedAssemblyData_37 = internal dso_local global [47368 x i8] zeroinitializer, align 16
@__compressedAssemblyData_38 = internal dso_local global [49312 x i8] zeroinitializer, align 16
@__compressedAssemblyData_39 = internal dso_local global [48816 x i8] zeroinitializer, align 16
@__compressedAssemblyData_40 = internal dso_local global [49824 x i8] zeroinitializer, align 16
@__compressedAssemblyData_41 = internal dso_local global [49840 x i8] zeroinitializer, align 16
@__compressedAssemblyData_42 = internal dso_local global [52912 x i8] zeroinitializer, align 16
@__compressedAssemblyData_43 = internal dso_local global [49824 x i8] zeroinitializer, align 16
@__compressedAssemblyData_44 = internal dso_local global [49824 x i8] zeroinitializer, align 16
@__compressedAssemblyData_45 = internal dso_local global [48288 x i8] zeroinitializer, align 16
@__compressedAssemblyData_46 = internal dso_local global [59552 x i8] zeroinitializer, align 16
@__compressedAssemblyData_47 = internal dso_local global [47776 x i8] zeroinitializer, align 16
@__compressedAssemblyData_48 = internal dso_local global [44720 x i8] zeroinitializer, align 16
@__compressedAssemblyData_49 = internal dso_local global [44704 x i8] zeroinitializer, align 16
@__compressedAssemblyData_50 = internal dso_local global [423072 x i8] zeroinitializer, align 16
@__compressedAssemblyData_51 = internal dso_local global [452256 x i8] zeroinitializer, align 16
@__compressedAssemblyData_52 = internal dso_local global [443040 x i8] zeroinitializer, align 16
@__compressedAssemblyData_53 = internal dso_local global [453792 x i8] zeroinitializer, align 16
@__compressedAssemblyData_54 = internal dso_local global [449184 x i8] zeroinitializer, align 16
@__compressedAssemblyData_55 = internal dso_local global [495392 x i8] zeroinitializer, align 16
@__compressedAssemblyData_56 = internal dso_local global [454408 x i8] zeroinitializer, align 16
@__compressedAssemblyData_57 = internal dso_local global [455328 x i8] zeroinitializer, align 16
@__compressedAssemblyData_58 = internal dso_local global [434848 x i8] zeroinitializer, align 16
@__compressedAssemblyData_59 = internal dso_local global [598704 x i8] zeroinitializer, align 16
@__compressedAssemblyData_60 = internal dso_local global [431264 x i8] zeroinitializer, align 16
@__compressedAssemblyData_61 = internal dso_local global [383648 x i8] zeroinitializer, align 16
@__compressedAssemblyData_62 = internal dso_local global [383136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_63 = internal dso_local global [49664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_64 = internal dso_local global [1320448 x i8] zeroinitializer, align 16
@__compressedAssemblyData_65 = internal dso_local global [3072 x i8] zeroinitializer, align 16
@__compressedAssemblyData_66 = internal dso_local global [308024 x i8] zeroinitializer, align 16
@__compressedAssemblyData_67 = internal dso_local global [430360 x i8] zeroinitializer, align 16
@__compressedAssemblyData_68 = internal dso_local global [17704 x i8] zeroinitializer, align 16
@__compressedAssemblyData_69 = internal dso_local global [15624 x i8] zeroinitializer, align 16
@__compressedAssemblyData_70 = internal dso_local global [33560 x i8] zeroinitializer, align 16
@__compressedAssemblyData_71 = internal dso_local global [15624 x i8] zeroinitializer, align 16
@__compressedAssemblyData_72 = internal dso_local global [15664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_73 = internal dso_local global [89912 x i8] zeroinitializer, align 16
@__compressedAssemblyData_74 = internal dso_local global [255792 x i8] zeroinitializer, align 16
@__compressedAssemblyData_75 = internal dso_local global [48400 x i8] zeroinitializer, align 16
@__compressedAssemblyData_76 = internal dso_local global [48392 x i8] zeroinitializer, align 16
@__compressedAssemblyData_77 = internal dso_local global [126728 x i8] zeroinitializer, align 16
@__compressedAssemblyData_78 = internal dso_local global [102688 x i8] zeroinitializer, align 16
@__compressedAssemblyData_79 = internal dso_local global [17160 x i8] zeroinitializer, align 16
@__compressedAssemblyData_80 = internal dso_local global [26888 x i8] zeroinitializer, align 16
@__compressedAssemblyData_81 = internal dso_local global [42264 x i8] zeroinitializer, align 16
@__compressedAssemblyData_82 = internal dso_local global [315688 x i8] zeroinitializer, align 16
@__compressedAssemblyData_83 = internal dso_local global [16664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_84 = internal dso_local global [19720 x i8] zeroinitializer, align 16
@__compressedAssemblyData_85 = internal dso_local global [51000 x i8] zeroinitializer, align 16
@__compressedAssemblyData_86 = internal dso_local global [23840 x i8] zeroinitializer, align 16
@__compressedAssemblyData_87 = internal dso_local global [1016616 x i8] zeroinitializer, align 16
@__compressedAssemblyData_88 = internal dso_local global [16168 x i8] zeroinitializer, align 16
@__compressedAssemblyData_89 = internal dso_local global [25400 x i8] zeroinitializer, align 16
@__compressedAssemblyData_90 = internal dso_local global [16680 x i8] zeroinitializer, align 16
@__compressedAssemblyData_91 = internal dso_local global [16168 x i8] zeroinitializer, align 16
@__compressedAssemblyData_92 = internal dso_local global [184600 x i8] zeroinitializer, align 16
@__compressedAssemblyData_93 = internal dso_local global [29496 x i8] zeroinitializer, align 16
@__compressedAssemblyData_94 = internal dso_local global [127256 x i8] zeroinitializer, align 16
@__compressedAssemblyData_95 = internal dso_local global [26384 x i8] zeroinitializer, align 16
@__compressedAssemblyData_96 = internal dso_local global [32048 x i8] zeroinitializer, align 16
@__compressedAssemblyData_97 = internal dso_local global [15632 x i8] zeroinitializer, align 16
@__compressedAssemblyData_98 = internal dso_local global [59176 x i8] zeroinitializer, align 16
@__compressedAssemblyData_99 = internal dso_local global [16680 x i8] zeroinitializer, align 16
@__compressedAssemblyData_100 = internal dso_local global [64816 x i8] zeroinitializer, align 16
@__compressedAssemblyData_101 = internal dso_local global [20744 x i8] zeroinitializer, align 16
@__compressedAssemblyData_102 = internal dso_local global [16680 x i8] zeroinitializer, align 16
@__compressedAssemblyData_103 = internal dso_local global [96536 x i8] zeroinitializer, align 16
@__compressedAssemblyData_104 = internal dso_local global [121608 x i8] zeroinitializer, align 16
@__compressedAssemblyData_105 = internal dso_local global [16152 x i8] zeroinitializer, align 16
@__compressedAssemblyData_106 = internal dso_local global [15664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_107 = internal dso_local global [16136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_108 = internal dso_local global [41224 x i8] zeroinitializer, align 16
@__compressedAssemblyData_109 = internal dso_local global [15664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_110 = internal dso_local global [38184 x i8] zeroinitializer, align 16
@__compressedAssemblyData_111 = internal dso_local global [110344 x i8] zeroinitializer, align 16
@__compressedAssemblyData_112 = internal dso_local global [32560 x i8] zeroinitializer, align 16
@__compressedAssemblyData_113 = internal dso_local global [48440 x i8] zeroinitializer, align 16
@__compressedAssemblyData_114 = internal dso_local global [15624 x i8] zeroinitializer, align 16
@__compressedAssemblyData_115 = internal dso_local global [55072 x i8] zeroinitializer, align 16
@__compressedAssemblyData_116 = internal dso_local global [16176 x i8] zeroinitializer, align 16
@__compressedAssemblyData_117 = internal dso_local global [43800 x i8] zeroinitializer, align 16
@__compressedAssemblyData_118 = internal dso_local global [48920 x i8] zeroinitializer, align 16
@__compressedAssemblyData_119 = internal dso_local global [78640 x i8] zeroinitializer, align 16
@__compressedAssemblyData_120 = internal dso_local global [23816 x i8] zeroinitializer, align 16
@__compressedAssemblyData_121 = internal dso_local global [67888 x i8] zeroinitializer, align 16
@__compressedAssemblyData_122 = internal dso_local global [15640 x i8] zeroinitializer, align 16
@__compressedAssemblyData_123 = internal dso_local global [16184 x i8] zeroinitializer, align 16
@__compressedAssemblyData_124 = internal dso_local global [575768 x i8] zeroinitializer, align 16
@__compressedAssemblyData_125 = internal dso_local global [223528 x i8] zeroinitializer, align 16
@__compressedAssemblyData_126 = internal dso_local global [76576 x i8] zeroinitializer, align 16
@__compressedAssemblyData_127 = internal dso_local global [149288 x i8] zeroinitializer, align 16
@__compressedAssemblyData_128 = internal dso_local global [56088 x i8] zeroinitializer, align 16
@__compressedAssemblyData_129 = internal dso_local global [56600 x i8] zeroinitializer, align 16
@__compressedAssemblyData_130 = internal dso_local global [676664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_131 = internal dso_local global [131856 x i8] zeroinitializer, align 16
@__compressedAssemblyData_132 = internal dso_local global [174864 x i8] zeroinitializer, align 16
@__compressedAssemblyData_133 = internal dso_local global [52016 x i8] zeroinitializer, align 16
@__compressedAssemblyData_134 = internal dso_local global [66352 x i8] zeroinitializer, align 16
@__compressedAssemblyData_135 = internal dso_local global [56088 x i8] zeroinitializer, align 16
@__compressedAssemblyData_136 = internal dso_local global [107320 x i8] zeroinitializer, align 16
@__compressedAssemblyData_137 = internal dso_local global [173352 x i8] zeroinitializer, align 16
@__compressedAssemblyData_138 = internal dso_local global [162056 x i8] zeroinitializer, align 16
@__compressedAssemblyData_139 = internal dso_local global [253728 x i8] zeroinitializer, align 16
@__compressedAssemblyData_140 = internal dso_local global [15648 x i8] zeroinitializer, align 16
@__compressedAssemblyData_141 = internal dso_local global [235296 x i8] zeroinitializer, align 16
@__compressedAssemblyData_142 = internal dso_local global [70936 x i8] zeroinitializer, align 16
@__compressedAssemblyData_143 = internal dso_local global [33576 x i8] zeroinitializer, align 16
@__compressedAssemblyData_144 = internal dso_local global [23832 x i8] zeroinitializer, align 16
@__compressedAssemblyData_145 = internal dso_local global [51984 x i8] zeroinitializer, align 16
@__compressedAssemblyData_146 = internal dso_local global [103184 x i8] zeroinitializer, align 16
@__compressedAssemblyData_147 = internal dso_local global [17688 x i8] zeroinitializer, align 16
@__compressedAssemblyData_148 = internal dso_local global [16176 x i8] zeroinitializer, align 16
@__compressedAssemblyData_149 = internal dso_local global [15672 x i8] zeroinitializer, align 16
@__compressedAssemblyData_150 = internal dso_local global [41736 x i8] zeroinitializer, align 16
@__compressedAssemblyData_151 = internal dso_local global [852280 x i8] zeroinitializer, align 16
@__compressedAssemblyData_152 = internal dso_local global [103216 x i8] zeroinitializer, align 16
@__compressedAssemblyData_153 = internal dso_local global [153880 x i8] zeroinitializer, align 16
@__compressedAssemblyData_154 = internal dso_local global [3099936 x i8] zeroinitializer, align 16
@__compressedAssemblyData_155 = internal dso_local global [38704 x i8] zeroinitializer, align 16
@__compressedAssemblyData_156 = internal dso_local global [16176 x i8] zeroinitializer, align 16
@__compressedAssemblyData_157 = internal dso_local global [16136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_158 = internal dso_local global [130344 x i8] zeroinitializer, align 16
@__compressedAssemblyData_159 = internal dso_local global [15664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_160 = internal dso_local global [501512 x i8] zeroinitializer, align 16
@__compressedAssemblyData_161 = internal dso_local global [16136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_162 = internal dso_local global [24360 x i8] zeroinitializer, align 16
@__compressedAssemblyData_163 = internal dso_local global [16648 x i8] zeroinitializer, align 16
@__compressedAssemblyData_164 = internal dso_local global [15648 x i8] zeroinitializer, align 16
@__compressedAssemblyData_165 = internal dso_local global [16184 x i8] zeroinitializer, align 16
@__compressedAssemblyData_166 = internal dso_local global [26888 x i8] zeroinitializer, align 16
@__compressedAssemblyData_167 = internal dso_local global [15664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_168 = internal dso_local global [17672 x i8] zeroinitializer, align 16
@__compressedAssemblyData_169 = internal dso_local global [18216 x i8] zeroinitializer, align 16
@__compressedAssemblyData_170 = internal dso_local global [15664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_171 = internal dso_local global [38672 x i8] zeroinitializer, align 16
@__compressedAssemblyData_172 = internal dso_local global [15656 x i8] zeroinitializer, align 16
@__compressedAssemblyData_173 = internal dso_local global [64784 x i8] zeroinitializer, align 16
@__compressedAssemblyData_174 = internal dso_local global [17704 x i8] zeroinitializer, align 16
@__compressedAssemblyData_175 = internal dso_local global [16136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_176 = internal dso_local global [143632 x i8] zeroinitializer, align 16
@__compressedAssemblyData_177 = internal dso_local global [66352 x i8] zeroinitializer, align 16
@__compressedAssemblyData_178 = internal dso_local global [16168 x i8] zeroinitializer, align 16
@__compressedAssemblyData_179 = internal dso_local global [23856 x i8] zeroinitializer, align 16
@__compressedAssemblyData_180 = internal dso_local global [17208 x i8] zeroinitializer, align 16
@__compressedAssemblyData_181 = internal dso_local global [17200 x i8] zeroinitializer, align 16
@__compressedAssemblyData_182 = internal dso_local global [44840 x i8] zeroinitializer, align 16
@__compressedAssemblyData_183 = internal dso_local global [58664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_184 = internal dso_local global [54040 x i8] zeroinitializer, align 16
@__compressedAssemblyData_185 = internal dso_local global [17688 x i8] zeroinitializer, align 16
@__compressedAssemblyData_186 = internal dso_local global [16664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_187 = internal dso_local global [16184 x i8] zeroinitializer, align 16
@__compressedAssemblyData_188 = internal dso_local global [16136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_189 = internal dso_local global [15624 x i8] zeroinitializer, align 16
@__compressedAssemblyData_190 = internal dso_local global [16136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_191 = internal dso_local global [17200 x i8] zeroinitializer, align 16
@__compressedAssemblyData_192 = internal dso_local global [705336 x i8] zeroinitializer, align 16
@__compressedAssemblyData_193 = internal dso_local global [38176 x i8] zeroinitializer, align 16
@__compressedAssemblyData_194 = internal dso_local global [15664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_195 = internal dso_local global [15664 x i8] zeroinitializer, align 16
@__compressedAssemblyData_196 = internal dso_local global [18736 x i8] zeroinitializer, align 16
@__compressedAssemblyData_197 = internal dso_local global [17200 x i8] zeroinitializer, align 16
@__compressedAssemblyData_198 = internal dso_local global [16136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_199 = internal dso_local global [741168 x i8] zeroinitializer, align 16
@__compressedAssemblyData_200 = internal dso_local global [16136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_201 = internal dso_local global [16136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_202 = internal dso_local global [70408 x i8] zeroinitializer, align 16
@__compressedAssemblyData_203 = internal dso_local global [617776 x i8] zeroinitializer, align 16
@__compressedAssemblyData_204 = internal dso_local global [369432 x i8] zeroinitializer, align 16
@__compressedAssemblyData_205 = internal dso_local global [57096 x i8] zeroinitializer, align 16
@__compressedAssemblyData_206 = internal dso_local global [16184 x i8] zeroinitializer, align 16
@__compressedAssemblyData_207 = internal dso_local global [186136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_208 = internal dso_local global [16176 x i8] zeroinitializer, align 16
@__compressedAssemblyData_209 = internal dso_local global [61704 x i8] zeroinitializer, align 16
@__compressedAssemblyData_210 = internal dso_local global [17200 x i8] zeroinitializer, align 16
@__compressedAssemblyData_211 = internal dso_local global [16160 x i8] zeroinitializer, align 16
@__compressedAssemblyData_212 = internal dso_local global [16176 x i8] zeroinitializer, align 16
@__compressedAssemblyData_213 = internal dso_local global [15656 x i8] zeroinitializer, align 16
@__compressedAssemblyData_214 = internal dso_local global [45328 x i8] zeroinitializer, align 16
@__compressedAssemblyData_215 = internal dso_local global [175912 x i8] zeroinitializer, align 16
@__compressedAssemblyData_216 = internal dso_local global [16672 x i8] zeroinitializer, align 16
@__compressedAssemblyData_217 = internal dso_local global [15656 x i8] zeroinitializer, align 16
@__compressedAssemblyData_218 = internal dso_local global [30520 x i8] zeroinitializer, align 16
@__compressedAssemblyData_219 = internal dso_local global [15656 x i8] zeroinitializer, align 16
@__compressedAssemblyData_220 = internal dso_local global [16168 x i8] zeroinitializer, align 16
@__compressedAssemblyData_221 = internal dso_local global [16136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_222 = internal dso_local global [22320 x i8] zeroinitializer, align 16
@__compressedAssemblyData_223 = internal dso_local global [16680 x i8] zeroinitializer, align 16
@__compressedAssemblyData_224 = internal dso_local global [16184 x i8] zeroinitializer, align 16
@__compressedAssemblyData_225 = internal dso_local global [16184 x i8] zeroinitializer, align 16
@__compressedAssemblyData_226 = internal dso_local global [16184 x i8] zeroinitializer, align 16
@__compressedAssemblyData_227 = internal dso_local global [16136 x i8] zeroinitializer, align 16
@__compressedAssemblyData_228 = internal dso_local global [18216 x i8] zeroinitializer, align 16
@__compressedAssemblyData_229 = internal dso_local global [23848 x i8] zeroinitializer, align 16
@__compressedAssemblyData_230 = internal dso_local global [50984 x i8] zeroinitializer, align 16
@__compressedAssemblyData_231 = internal dso_local global [16680 x i8] zeroinitializer, align 16
@__compressedAssemblyData_232 = internal dso_local global [60192 x i8] zeroinitializer, align 16
@__compressedAssemblyData_233 = internal dso_local global [101160 x i8] zeroinitializer, align 16
@__compressedAssemblyData_234 = internal dso_local global [240176 x i8] zeroinitializer, align 16
@__compressedAssemblyData_235 = internal dso_local global [82976 x i8] zeroinitializer, align 16
@__compressedAssemblyData_236 = internal dso_local global [18976 x i8] zeroinitializer, align 16
@__compressedAssemblyData_237 = internal dso_local global [37449272 x i8] zeroinitializer, align 16
@__compressedAssemblyData_238 = internal dso_local global [4776744 x i8] zeroinitializer, align 16

; Metadata
!llvm.module.flags = !{!0, !1}
!0 = !{i32 1, !"wchar_size", i32 4}
!1 = !{i32 7, !"PIC Level", i32 2}
!llvm.ident = !{!2}
!2 = !{!".NET for Android remotes/origin/release/9.0.1xx @ e7876a4f92d894b40c191a24c2b74f06d4bf4573"}
!3 = !{!4, !4, i64 0}
!4 = !{!"any pointer", !5, i64 0}
!5 = !{!"omnipotent char", !6, i64 0}
!6 = !{!"Simple C++ TBAA"}
