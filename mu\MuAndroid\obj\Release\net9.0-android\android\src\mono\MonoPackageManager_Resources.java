package mono;
public class MonoPackageManager_Resources {
	public static String[] Assemblies = new String[]{
		/* We need to ensure that "MuAndroid.dll" comes first in this list. */
		"MuAndroid.dll",
		"BCnEncoder.dll",
		"BouncyCastle.Crypto.dll",
		"CommunityToolkit.HighPerformance.dll",
		"Delizious.Ini.dll",
		"INIFileParser.dll",
		"LEA.NET.dll",
		"Microsoft.CodeAnalysis.dll",
		"Microsoft.CodeAnalysis.CSharp.dll",
		"Microsoft.Extensions.Configuration.dll",
		"Microsoft.Extensions.Configuration.Abstractions.dll",
		"Microsoft.Extensions.Configuration.Binder.dll",
		"Microsoft.Extensions.Configuration.FileExtensions.dll",
		"Microsoft.Extensions.Configuration.Json.dll",
		"Microsoft.Extensions.DependencyInjection.dll",
		"Microsoft.Extensions.DependencyInjection.Abstractions.dll",
		"Microsoft.Extensions.FileProviders.Abstractions.dll",
		"Microsoft.Extensions.FileProviders.Physical.dll",
		"Microsoft.Extensions.FileSystemGlobbing.dll",
		"Microsoft.Extensions.Logging.dll",
		"Microsoft.Extensions.Logging.Abstractions.dll",
		"Microsoft.Extensions.Logging.Configuration.dll",
		"Microsoft.Extensions.Logging.Console.dll",
		"Microsoft.Extensions.Options.dll",
		"Microsoft.Extensions.Options.ConfigurationExtensions.dll",
		"Microsoft.Extensions.Primitives.dll",
		"MonoGame.Framework.dll",
		"MUnique.OpenMU.Network.dll",
		"MUnique.OpenMU.Network.Packets.dll",
		"MUnique.OpenMU.PlugIns.dll",
		"Nito.AsyncEx.Coordination.dll",
		"Nito.AsyncEx.Tasks.dll",
		"Nito.Collections.Deque.dll",
		"Nito.Disposables.dll",
		"NLayer.dll",
		"Pipelines.Sockets.Unofficial.dll",
		"SixLabors.ImageSharp.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Client.Data.dll",
		"Client.Main.dll",
		"_Microsoft.Android.Resource.Designer.dll",
		"BCnEncoder.dll",
		"BouncyCastle.Crypto.dll",
		"CommunityToolkit.HighPerformance.dll",
		"Delizious.Ini.dll",
		"INIFileParser.dll",
		"LEA.NET.dll",
		"Microsoft.CodeAnalysis.dll",
		"Microsoft.CodeAnalysis.CSharp.dll",
		"Microsoft.Extensions.Configuration.dll",
		"Microsoft.Extensions.Configuration.Abstractions.dll",
		"Microsoft.Extensions.Configuration.Binder.dll",
		"Microsoft.Extensions.Configuration.FileExtensions.dll",
		"Microsoft.Extensions.Configuration.Json.dll",
		"Microsoft.Extensions.DependencyInjection.dll",
		"Microsoft.Extensions.DependencyInjection.Abstractions.dll",
		"Microsoft.Extensions.FileProviders.Abstractions.dll",
		"Microsoft.Extensions.FileProviders.Physical.dll",
		"Microsoft.Extensions.FileSystemGlobbing.dll",
		"Microsoft.Extensions.Logging.dll",
		"Microsoft.Extensions.Logging.Abstractions.dll",
		"Microsoft.Extensions.Logging.Configuration.dll",
		"Microsoft.Extensions.Logging.Console.dll",
		"Microsoft.Extensions.Options.dll",
		"Microsoft.Extensions.Options.ConfigurationExtensions.dll",
		"Microsoft.Extensions.Primitives.dll",
		"MonoGame.Framework.dll",
		"MUnique.OpenMU.Network.dll",
		"MUnique.OpenMU.Network.Packets.dll",
		"MUnique.OpenMU.PlugIns.dll",
		"Nito.AsyncEx.Coordination.dll",
		"Nito.AsyncEx.Tasks.dll",
		"Nito.Collections.Deque.dll",
		"Nito.Disposables.dll",
		"NLayer.dll",
		"Pipelines.Sockets.Unofficial.dll",
		"SixLabors.ImageSharp.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Microsoft.CodeAnalysis.CSharp.resources.dll",
		"Client.Data.dll",
		"Client.Main.dll",
		"_Microsoft.Android.Resource.Designer.dll",
	};
	public static String[] Dependencies = new String[]{
	};
}
