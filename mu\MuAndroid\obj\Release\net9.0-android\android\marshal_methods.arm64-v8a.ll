; ModuleID = 'marshal_methods.arm64-v8a.ll'
source_filename = "marshal_methods.arm64-v8a.ll"
target datalayout = "e-m:e-i8:8:32-i16:16:32-i64:64-i128:128-n32:64-S128"
target triple = "aarch64-unknown-linux-android21"

%struct.MarshalMethodName = type {
	i64, ; uint64_t id
	ptr ; char* name
}

%struct.MarshalMethodsManagedClass = type {
	i32, ; uint32_t token
	ptr ; MonoClass klass
}

@assembly_image_cache = dso_local local_unnamed_addr global [239 x ptr] zeroinitializer, align 8

; Each entry maps hash of an assembly name to an index into the `assembly_image_cache` array
@assembly_image_cache_hashes = dso_local local_unnamed_addr constant [717 x i64] [
	i64 u0x001e58127c546039, ; 0: lib_System.Globalization.dll.so => 42
	i64 u0x01109b0e4d99e61f, ; 1: System.ComponentModel.Annotations.dll => 13
	i64 u0x022f31be406de945, ; 2: Microsoft.Extensions.Options.ConfigurationExtensions => 197
	i64 u0x0284512fad379f7e, ; 3: System.Runtime.Handles => 105
	i64 u0x02abedc11addc1ed, ; 4: lib_Mono.Android.Runtime.dll.so => 171
	i64 u0x02f55bf70672f5c8, ; 5: lib_System.IO.FileSystem.DriveInfo.dll.so => 48
	i64 u0x03621c804933a890, ; 6: System.Buffers => 7
	i64 u0x0399610510a38a38, ; 7: lib_System.Private.DataContractSerialization.dll.so => 86
	i64 u0x0517ef04e06e9f76, ; 8: System.Net.Primitives => 71
	i64 u0x0581db89237110e9, ; 9: lib_System.Collections.dll.so => 12
	i64 u0x05a1c25e78e22d87, ; 10: lib_System.Runtime.CompilerServices.Unsafe.dll.so => 102
	i64 u0x06388ffe9f6c161a, ; 11: System.Xml.Linq.dll => 156
	i64 u0x06600c4c124cb358, ; 12: System.Configuration.dll => 19
	i64 u0x069fff96ec92a91d, ; 13: System.Xml.XPath.dll => 161
	i64 u0x07469f2eecce9e85, ; 14: mscorlib.dll => 167
	i64 u0x07dcdc7460a0c5e4, ; 15: System.Collections.NonGeneric => 10
	i64 u0x08a7c865576bbde7, ; 16: System.Reflection.Primitives => 96
	i64 u0x09138715c92dba90, ; 17: lib_System.ComponentModel.Annotations.dll.so => 13
	i64 u0x0919c28b89381a0b, ; 18: lib_Microsoft.Extensions.Options.dll.so => 196
	i64 u0x092266563089ae3e, ; 19: lib_System.Collections.NonGeneric.dll.so => 10
	i64 u0x09d144a7e214d457, ; 20: System.Security.Cryptography => 127
	i64 u0x09e2b9f743db21a8, ; 21: lib_System.Reflection.Metadata.dll.so => 95
	i64 u0x0abb3e2b271edc45, ; 22: System.Threading.Channels.dll => 140
	i64 u0x0b06b1feab070143, ; 23: System.Formats.Tar => 39
	i64 u0x0c0edaaebec7f4bb, ; 24: Microsoft.Extensions.Logging.Console => 195
	i64 u0x0c59ad9fbbd43abe, ; 25: Mono.Android => 172
	i64 u0x0c74af560004e816, ; 26: Microsoft.Win32.Registry.dll => 5
	i64 u0x0c83c82812e96127, ; 27: lib_System.Net.Mail.dll.so => 67
	i64 u0x0cf6a95dadccbb9c, ; 28: zh-Hant/Microsoft.CodeAnalysis.resources.dll => 222
	i64 u0x0d13cd7cce4284e4, ; 29: System.Security.SecureString => 130
	i64 u0x0e14e73a54dda68e, ; 30: lib_System.Net.NameResolution.dll.so => 68
	i64 u0x0e7acf675d09f75a, ; 31: it/Microsoft.CodeAnalysis.resources => 214
	i64 u0x0ec47e16319c99d9, ; 32: lib-de-Microsoft.CodeAnalysis.resources.dll.so => 211
	i64 u0x0f5e7abaa7cf470a, ; 33: System.Net.HttpListener => 66
	i64 u0x1001f97bbe242e64, ; 34: System.IO.UnmanagedMemoryStream => 57
	i64 u0x105b053cfbaba1f0, ; 35: lib_Microsoft.CodeAnalysis.dll.so => 180
	i64 u0x1065c4cb554c3d75, ; 36: System.IO.IsolatedStorage.dll => 52
	i64 u0x10a579e648829775, ; 37: Microsoft.CodeAnalysis => 180
	i64 u0x10f6cfcbcf801616, ; 38: System.IO.Compression.Brotli => 43
	i64 u0x114443cdcf2091f1, ; 39: System.Security.Cryptography.Primitives => 125
	i64 u0x11477c248c4777fc, ; 40: MUnique.OpenMU.Network => 200
	i64 u0x114df3ff11650a65, ; 41: ru/Microsoft.CodeAnalysis.CSharp.resources => 232
	i64 u0x11a603952763e1d4, ; 42: System.Net.Mail => 67
	i64 u0x11a70d0e1009fb11, ; 43: System.Net.WebSockets.dll => 81
	i64 u0x1208da3842d90ff3, ; 44: lib-ko-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 229
	i64 u0x12128b3f59302d47, ; 45: lib_System.Xml.Serialization.dll.so => 158
	i64 u0x123639456fb056da, ; 46: System.Reflection.Emit.Lightweight.dll => 92
	i64 u0x12521e9764603eaa, ; 47: lib_System.Resources.Reader.dll.so => 99
	i64 u0x12d3b63863d4ab0b, ; 48: lib_System.Threading.Overlapped.dll.so => 141
	i64 u0x131463e9417f52d4, ; 49: de/Microsoft.CodeAnalysis.CSharp.resources => 224
	i64 u0x134eab1061c395ee, ; 50: System.Transactions => 151
	i64 u0x1393617ead22674a, ; 51: zh-Hant/Microsoft.CodeAnalysis.resources => 222
	i64 u0x13beedefb0e28a45, ; 52: lib_System.Xml.XmlDocument.dll.so => 162
	i64 u0x13f1e5e209e91af4, ; 53: lib_Java.Interop.dll.so => 169
	i64 u0x143d8ea60a6a4011, ; 54: Microsoft.Extensions.DependencyInjection.Abstractions => 188
	i64 u0x1446c7a06695f3ea, ; 55: ko/Microsoft.CodeAnalysis.CSharp.resources.dll => 229
	i64 u0x1497051b917530bd, ; 56: lib_System.Net.WebSockets.dll.so => 81
	i64 u0x1506378c0000a92a, ; 57: lib-tr-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 233
	i64 u0x152a448bd1e745a7, ; 58: Microsoft.Win32.Primitives => 4
	i64 u0x1557de0138c445f4, ; 59: lib_Microsoft.Win32.Registry.dll.so => 5
	i64 u0x15bdc156ed462f2f, ; 60: lib_System.IO.FileSystem.dll.so => 51
	i64 u0x15e300c2c1668655, ; 61: System.Resources.Writer.dll => 101
	i64 u0x16bf2a22df043a09, ; 62: System.IO.Pipes.dll => 56
	i64 u0x16cd84ceca3b17f3, ; 63: lib_BouncyCastle.Crypto.dll.so => 175
	i64 u0x16ea2b318ad2d830, ; 64: System.Security.Cryptography.Algorithms => 120
	i64 u0x16eeae54c7ebcc08, ; 65: System.Reflection.dll => 98
	i64 u0x17125c9a85b4929f, ; 66: lib_netstandard.dll.so => 168
	i64 u0x1716866f7416792e, ; 67: lib_System.Security.AccessControl.dll.so => 118
	i64 u0x1752c12f1e1fc00c, ; 68: System.Core => 21
	i64 u0x17f9358913beb16a, ; 69: System.Text.Encodings.Web => 137
	i64 u0x1809fb23f29ba44a, ; 70: lib_System.Reflection.TypeExtensions.dll.so => 97
	i64 u0x18950fae1c2bc98e, ; 71: lib-cs-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 223
	i64 u0x18a9befae51bb361, ; 72: System.Net.WebClient => 77
	i64 u0x192712eaa333180f, ; 73: lib-zh-Hant-Microsoft.CodeAnalysis.resources.dll.so => 222
	i64 u0x19a4c090f14ebb66, ; 74: System.Security.Claims => 119
	i64 u0x1a761daba47c6ad5, ; 75: ja/Microsoft.CodeAnalysis.resources.dll => 215
	i64 u0x1a91866a319e9259, ; 76: lib_System.Collections.Concurrent.dll.so => 8
	i64 u0x1a9e139e4762aaf8, ; 77: es/Microsoft.CodeAnalysis.CSharp.resources.dll => 225
	i64 u0x1aac34d1917ba5d3, ; 78: lib_System.dll.so => 165
	i64 u0x1aea8f1c3b282172, ; 79: lib_System.Net.Ping.dll.so => 70
	i64 u0x1c074bdeeae2e1c9, ; 80: lib-pl-Microsoft.CodeAnalysis.resources.dll.so => 217
	i64 u0x1c5217a9e4973753, ; 81: lib_Microsoft.Extensions.FileProviders.Physical.dll.so => 190
	i64 u0x1c753b5ff15bce1b, ; 82: Mono.Android.Runtime.dll => 171
	i64 u0x1cd47467799d8250, ; 83: System.Threading.Tasks.dll => 145
	i64 u0x1d23eafdc6dc346c, ; 84: System.Globalization.Calendars.dll => 40
	i64 u0x1db6820994506bf5, ; 85: System.IO.FileSystem.AccessControl.dll => 47
	i64 u0x1dbb0c2c6a999acb, ; 86: System.Diagnostics.StackTrace => 30
	i64 u0x1e7c31185e2fb266, ; 87: lib_System.Threading.Tasks.Parallel.dll.so => 144
	i64 u0x1ed8fcce5e9b50a0, ; 88: Microsoft.Extensions.Options.dll => 196
	i64 u0x1f055d15d807e1b2, ; 89: System.Xml.XmlSerializer => 163
	i64 u0x1f1ed22c1085f044, ; 90: lib_System.Diagnostics.FileVersionInfo.dll.so => 28
	i64 u0x1f61df9c5b94d2c1, ; 91: lib_System.Numerics.dll.so => 84
	i64 u0x20237ea48006d7a8, ; 92: lib_System.Net.WebClient.dll.so => 77
	i64 u0x209375905fcc1bad, ; 93: lib_System.IO.Compression.Brotli.dll.so => 43
	i64 u0x20fab3cf2dfbc8df, ; 94: lib_System.Diagnostics.Process.dll.so => 29
	i64 u0x2110167c128cba15, ; 95: System.Globalization => 42
	i64 u0x21419508838f7547, ; 96: System.Runtime.CompilerServices.VisualC => 103
	i64 u0x2174319c0d835bc9, ; 97: System.Runtime => 117
	i64 u0x219ea1b751a4dee4, ; 98: lib_System.IO.Compression.ZipFile.dll.so => 45
	i64 u0x21cc7e445dcd5469, ; 99: System.Reflection.Emit.ILGeneration => 91
	i64 u0x224538d85ed15a82, ; 100: System.IO.Pipes => 56
	i64 u0x22908438c6bed1af, ; 101: lib_System.Threading.Timer.dll.so => 148
	i64 u0x237be844f1f812c7, ; 102: System.Threading.Thread.dll => 146
	i64 u0x23852b3bdc9f7096, ; 103: System.Resources.ResourceManager => 100
	i64 u0x23986dd7e5d4fc01, ; 104: System.IO.FileSystem.Primitives.dll => 49
	i64 u0x2407aef2bbe8fadf, ; 105: System.Console => 20
	i64 u0x2453317c0144c67e, ; 106: Client.Data.dll => 236
	i64 u0x245ebc45bf698558, ; 107: ru/Microsoft.CodeAnalysis.resources.dll => 219
	i64 u0x247619fe4413f8bf, ; 108: System.Runtime.Serialization.Primitives.dll => 114
	i64 u0x268c1439f13bcc29, ; 109: lib_Microsoft.Extensions.Primitives.dll.so => 198
	i64 u0x26a670e154a9c54b, ; 110: System.Reflection.Extensions.dll => 94
	i64 u0x26d077d9678fe34f, ; 111: System.IO.dll => 58
	i64 u0x272377f9edc266a2, ; 112: tr/Microsoft.CodeAnalysis.resources => 220
	i64 u0x2759af78ab94d39b, ; 113: System.Net.WebSockets => 81
	i64 u0x27b410442fad6cf1, ; 114: Java.Interop.dll => 169
	i64 u0x27b97e0d52c3034a, ; 115: System.Diagnostics.Debug => 26
	i64 u0x2801845a2c71fbfb, ; 116: System.Net.Primitives.dll => 71
	i64 u0x2a3b095612184159, ; 117: lib_System.Net.NetworkInformation.dll.so => 69
	i64 u0x2a6507a5ffabdf28, ; 118: System.Diagnostics.TraceSource.dll => 33
	i64 u0x2ad5d6b13b7a3e04, ; 119: System.ComponentModel.DataAnnotations.dll => 14
	i64 u0x2af298f63581d886, ; 120: System.Text.RegularExpressions.dll => 139
	i64 u0x2afc1c4f898552ee, ; 121: lib_System.Formats.Asn1.dll.so => 38
	i64 u0x2b4d4904cebfa4e9, ; 122: Microsoft.Extensions.FileSystemGlobbing => 191
	i64 u0x2bc2c8499f169046, ; 123: SixLabors.ImageSharp => 209
	i64 u0x2c52b6785191227e, ; 124: lib_Microsoft.Extensions.Logging.Configuration.dll.so => 194
	i64 u0x2cbd9262ca785540, ; 125: lib_System.Text.Encoding.CodePages.dll.so => 134
	i64 u0x2cc9e1fed6257257, ; 126: lib_System.Reflection.Emit.Lightweight.dll.so => 92
	i64 u0x2cd723e9fe623c7c, ; 127: lib_System.Private.Xml.Linq.dll.so => 88
	i64 u0x2d169d318a968379, ; 128: System.Threading.dll => 149
	i64 u0x2d5ffcae1ad0aaca, ; 129: System.Data.dll => 24
	i64 u0x2db915caf23548d2, ; 130: System.Text.Json.dll => 138
	i64 u0x2dcaa0bb15a4117a, ; 131: System.IO.UnmanagedMemoryStream.dll => 57
	i64 u0x2e4d2e03e610a6e9, ; 132: pl/Microsoft.CodeAnalysis.resources => 217
	i64 u0x2e5a40c319acb800, ; 133: System.IO.FileSystem => 51
	i64 u0x2f02f94df3200fe5, ; 134: System.Diagnostics.Process => 29
	i64 u0x2f2e98e1c89b1aff, ; 135: System.Xml.ReaderWriter => 157
	i64 u0x2f5911d9ba814e4e, ; 136: System.Diagnostics.Tracing => 34
	i64 u0x2f84070a459bc31f, ; 137: lib_System.Xml.dll.so => 164
	i64 u0x30c6dda129408828, ; 138: System.IO.IsolatedStorage => 52
	i64 u0x31195fef5d8fb552, ; 139: _Microsoft.Android.Resource.Designer.dll => 238
	i64 u0x31496b779ed0663d, ; 140: lib_System.Reflection.DispatchProxy.dll.so => 90
	i64 u0x3235427f8d12dae1, ; 141: lib_System.Drawing.Primitives.dll.so => 35
	i64 u0x324622a9fd95b0c8, ; 142: lib-cs-Microsoft.CodeAnalysis.resources.dll.so => 210
	i64 u0x32aa989ff07a84ff, ; 143: lib_System.Xml.ReaderWriter.dll.so => 157
	i64 u0x33642d5508314e46, ; 144: Microsoft.Extensions.FileSystemGlobbing.dll => 191
	i64 u0x33829542f112d59b, ; 145: System.Collections.Immutable => 9
	i64 u0x341abc357fbb4ebf, ; 146: lib_System.Net.Sockets.dll.so => 76
	i64 u0x3496c1e2dcaf5ecc, ; 147: lib_System.IO.Pipes.AccessControl.dll.so => 55
	i64 u0x34ef56e1435b2843, ; 148: pl/Microsoft.CodeAnalysis.CSharp.resources.dll => 230
	i64 u0x353590da528c9d22, ; 149: System.ComponentModel.Annotations => 13
	i64 u0x355282fc1c909694, ; 150: Microsoft.Extensions.Configuration => 182
	i64 u0x355c649948d55d97, ; 151: lib_System.Runtime.Intrinsics.dll.so => 109
	i64 u0x35766456ffb7a7b4, ; 152: fr/Microsoft.CodeAnalysis.CSharp.resources.dll => 226
	i64 u0x3628ab68db23a01a, ; 153: lib_System.Diagnostics.Tools.dll.so => 32
	i64 u0x3673b042508f5b6b, ; 154: lib_System.Runtime.Extensions.dll.so => 104
	i64 u0x36740f1a8ecdc6c4, ; 155: System.Numerics => 84
	i64 u0x36b2b50fdf589ae2, ; 156: System.Reflection.Emit.Lightweight => 92
	i64 u0x36cada77dc79928b, ; 157: System.IO.MemoryMappedFiles => 53
	i64 u0x36db0dd9a0c9e54f, ; 158: Nito.AsyncEx.Tasks => 204
	i64 u0x374ef46b06791af6, ; 159: System.Reflection.Primitives.dll => 96
	i64 u0x37bc29f3183003b6, ; 160: lib_System.IO.dll.so => 58
	i64 u0x37da9065d1e32e30, ; 161: lib_LEA.NET.dll.so => 179
	i64 u0x380134e03b1e160a, ; 162: System.Collections.Immutable.dll => 9
	i64 u0x38049b5c59b39324, ; 163: System.Runtime.CompilerServices.Unsafe => 102
	i64 u0x38869c811d74050e, ; 164: System.Net.NameResolution.dll => 68
	i64 u0x39c3107c28752af1, ; 165: lib_Microsoft.Extensions.FileProviders.Abstractions.dll.so => 189
	i64 u0x3ab5859054645f72, ; 166: System.Security.Cryptography.Primitives.dll => 125
	i64 u0x3ae44ac43a1fbdbb, ; 167: System.Runtime.Serialization => 116
	i64 u0x3b860f9932505633, ; 168: lib_System.Text.Encoding.Extensions.dll.so => 135
	i64 u0x3c3aafb6b3a00bf6, ; 169: lib_System.Security.Cryptography.X509Certificates.dll.so => 126
	i64 u0x3c4049146b59aa90, ; 170: System.Runtime.InteropServices.JavaScript => 106
	i64 u0x3c7e5ed3d5db71bb, ; 171: System.Security => 131
	i64 u0x3d2b1913edfc08d7, ; 172: lib_System.Threading.ThreadPool.dll.so => 147
	i64 u0x3d46f0b995082740, ; 173: System.Xml.Linq => 156
	i64 u0x3db495de2204755c, ; 174: Microsoft.Extensions.Configuration.FileExtensions => 185
	i64 u0x3e54e185aa42d21f, ; 175: LEA.NET.dll => 179
	i64 u0x3e57d4d195c53c2e, ; 176: System.Reflection.TypeExtensions => 97
	i64 u0x3e616ab4ed1f3f15, ; 177: lib_System.Data.dll.so => 24
	i64 u0x3f510adf788828dd, ; 178: System.Threading.Tasks.Extensions => 143
	i64 u0x40c98b6bd77346d4, ; 179: Microsoft.VisualBasic.dll => 3
	i64 u0x415c502eb40e7418, ; 180: es/Microsoft.CodeAnalysis.resources.dll => 212
	i64 u0x41833cf766d27d96, ; 181: mscorlib => 167
	i64 u0x41dbb63b43b4bd84, ; 182: CommunityToolkit.HighPerformance => 176
	i64 u0x423a9ecc4d905a88, ; 183: lib_System.Resources.ResourceManager.dll.so => 100
	i64 u0x423bf51ae7def810, ; 184: System.Xml.XPath => 161
	i64 u0x42462ff15ddba223, ; 185: System.Resources.Reader.dll => 99
	i64 u0x42a31b86e6ccc3f0, ; 186: System.Diagnostics.Contracts => 25
	i64 u0x430e95b891249788, ; 187: lib_System.Reflection.Emit.dll.so => 93
	i64 u0x43375950ec7c1b6a, ; 188: netstandard.dll => 168
	i64 u0x434c4e1d9284cdae, ; 189: Mono.Android.dll => 172
	i64 u0x437d06c381ed575a, ; 190: lib_Microsoft.VisualBasic.dll.so => 3
	i64 u0x43ffdb4d66f61308, ; 191: Microsoft.Extensions.Logging.Console.dll => 195
	i64 u0x448bd33429269b19, ; 192: Microsoft.CSharp => 1
	i64 u0x4499fa3c8e494654, ; 193: lib_System.Runtime.Serialization.Primitives.dll.so => 114
	i64 u0x45c40276a42e283e, ; 194: System.Diagnostics.TraceSource => 33
	i64 u0x45d443f2a29adc37, ; 195: System.AppContext.dll => 6
	i64 u0x4656fb0d54b99d10, ; 196: MuAndroid => 0
	i64 u0x46e7dd2f263d5478, ; 197: NLayer.dll => 207
	i64 u0x472c10e45b4b6f81, ; 198: SixLabors.ImageSharp.dll => 209
	i64 u0x47358bd471172e1d, ; 199: lib_System.Xml.Linq.dll.so => 156
	i64 u0x475461b41cd2bae5, ; 200: lib-zh-Hant-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 235
	i64 u0x480c0a47dd42dd81, ; 201: lib_System.IO.MemoryMappedFiles.dll.so => 53
	i64 u0x490cffb50a3cc73e, ; 202: MonoGame.Framework => 199
	i64 u0x49daa9ded084ae15, ; 203: Nito.AsyncEx.Coordination => 203
	i64 u0x49e952f19a4e2022, ; 204: System.ObjectModel => 85
	i64 u0x4a1afd3bf9c69c98, ; 205: fr/Microsoft.CodeAnalysis.resources => 213
	i64 u0x4a7a18981dbd56bc, ; 206: System.IO.Compression.FileSystem.dll => 44
	i64 u0x4b07a0ed0ab33ff4, ; 207: System.Runtime.Extensions.dll => 104
	i64 u0x4b484a0d637947d7, ; 208: lib-zh-Hans-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 234
	i64 u0x4b558744a6e1abe0, ; 209: lib-de-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 224
	i64 u0x4b576d47ac054f3c, ; 210: System.IO.FileSystem.AccessControl => 47
	i64 u0x4b7b6532ded934b7, ; 211: System.Text.Json => 138
	i64 u0x4bef747f74f26a4f, ; 212: Nito.Collections.Deque.dll => 205
	i64 u0x4bf7c07326008291, ; 213: Client.Data => 236
	i64 u0x4c7755cf07ad2d5f, ; 214: System.Net.Http.Json.dll => 64
	i64 u0x4cf6f67dc77aacd2, ; 215: System.Net.NetworkInformation.dll => 69
	i64 u0x4d3183dd245425d4, ; 216: System.Net.WebSockets.Client.dll => 80
	i64 u0x4d479f968a05e504, ; 217: System.Linq.Expressions.dll => 59
	i64 u0x4d55a010ffc4faff, ; 218: System.Private.Xml => 89
	i64 u0x4d5cbe77561c5b2e, ; 219: System.Web.dll => 154
	i64 u0x4d7793536e79c309, ; 220: System.ServiceProcess => 133
	i64 u0x4d95fccc1f67c7ca, ; 221: System.Runtime.Loader.dll => 110
	i64 u0x4e32f00cb0937401, ; 222: Mono.Android.Runtime => 171
	i64 u0x4e5eea4668ac2b18, ; 223: System.Text.Encoding.CodePages => 134
	i64 u0x4e84220084ab2d20, ; 224: cs/Microsoft.CodeAnalysis.CSharp.resources.dll => 223
	i64 u0x4ebd0c4b82c5eefc, ; 225: lib_System.Threading.Channels.dll.so => 140
	i64 u0x4ee8eaa9c9c1151a, ; 226: System.Globalization.Calendars => 40
	i64 u0x4fdc964ec1888e25, ; 227: lib_Microsoft.Extensions.Configuration.Binder.dll.so => 184
	i64 u0x50c3a29b21050d45, ; 228: System.Linq.Parallel.dll => 60
	i64 u0x5116b21580ae6eb0, ; 229: Microsoft.Extensions.Configuration.Binder.dll => 184
	i64 u0x516324a5050a7e3c, ; 230: System.Net.WebProxy => 79
	i64 u0x516d6f0b21a303de, ; 231: lib_System.Diagnostics.Contracts.dll.so => 25
	i64 u0x51bb8a2afe774e32, ; 232: System.Drawing => 36
	i64 u0x5247c5c32a4140f0, ; 233: System.Resources.Reader => 99
	i64 u0x526ce79eb8e90527, ; 234: lib_System.Net.Primitives.dll.so => 71
	i64 u0x52829f00b4467c38, ; 235: lib_System.Data.Common.dll.so => 22
	i64 u0x533514f6711b299b, ; 236: ko/Microsoft.CodeAnalysis.CSharp.resources => 229
	i64 u0x53978aac584c666e, ; 237: lib_System.Security.Cryptography.Cng.dll.so => 121
	i64 u0x53a96d5c86c9e194, ; 238: System.Net.NetworkInformation => 69
	i64 u0x53be1038a61e8d44, ; 239: System.Runtime.InteropServices.RuntimeInformation.dll => 107
	i64 u0x5435e6f049e9bc37, ; 240: System.Security.Claims.dll => 119
	i64 u0x54795225dd1587af, ; 241: lib_System.Runtime.dll.so => 117
	i64 u0x54d75f85d6578cff, ; 242: lib-fr-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 226
	i64 u0x5588627c9a108ec9, ; 243: System.Collections.Specialized => 11
	i64 u0x55a898e4f42e3fae, ; 244: Microsoft.VisualBasic.Core.dll => 2
	i64 u0x55fa0c610fe93bb1, ; 245: lib_System.Security.Cryptography.OpenSsl.dll.so => 124
	i64 u0x56442b99bc64bb47, ; 246: System.Runtime.Serialization.Xml.dll => 115
	i64 u0x56a8b26e1aeae27b, ; 247: System.Threading.Tasks.Dataflow => 142
	i64 u0x56b07a7fba63b1f0, ; 248: LEA.NET => 179
	i64 u0x56f932d61e93c07f, ; 249: System.Globalization.Extensions => 41
	i64 u0x571c5cfbec5ae8e2, ; 250: System.Private.Uri => 87
	i64 u0x5724fbe6b45b7f07, ; 251: lib-pt-BR-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 231
	i64 u0x579a06fed6eec900, ; 252: System.Private.CoreLib.dll => 173
	i64 u0x57c542c14049b66d, ; 253: System.Diagnostics.DiagnosticSource => 27
	i64 u0x5818d9c133b37ec0, ; 254: BCnEncoder => 174
	i64 u0x581a8bd5cfda563e, ; 255: System.Threading.Timer => 148
	i64 u0x584ac38e21d2fde1, ; 256: Microsoft.Extensions.Configuration.Binder => 184
	i64 u0x5865790f44b93ba0, ; 257: lib_NLayer.dll.so => 207
	i64 u0x58688d9af496b168, ; 258: Microsoft.Extensions.DependencyInjection.dll => 187
	i64 u0x58ef0576630aa114, ; 259: fr/Microsoft.CodeAnalysis.CSharp.resources => 226
	i64 u0x595a356d23e8da9a, ; 260: lib_Microsoft.CSharp.dll.so => 1
	i64 u0x5a745f5101a75527, ; 261: lib_System.IO.Compression.FileSystem.dll.so => 44
	i64 u0x5a8f6699f4a1caa9, ; 262: lib_System.Threading.dll.so => 149
	i64 u0x5ae9cd33b15841bf, ; 263: System.ComponentModel => 18
	i64 u0x5b54391bdc6fcfe6, ; 264: System.Private.DataContractSerialization => 86
	i64 u0x5b8109e8e14c5e3e, ; 265: System.Globalization.Extensions.dll => 41
	i64 u0x5bb93c3ef9525c89, ; 266: es/Microsoft.CodeAnalysis.resources => 212
	i64 u0x5be34cb3cc2ff949, ; 267: tr/Microsoft.CodeAnalysis.CSharp.resources => 233
	i64 u0x5c30a4a35f9cc8c4, ; 268: lib_System.Reflection.Extensions.dll.so => 94
	i64 u0x5c393624b8176517, ; 269: lib_Microsoft.Extensions.Logging.dll.so => 192
	i64 u0x5c53c29f5073b0c9, ; 270: System.Diagnostics.FileVersionInfo => 28
	i64 u0x5c6724284a5e7317, ; 271: lib-tr-Microsoft.CodeAnalysis.resources.dll.so => 220
	i64 u0x5c87463c575c7616, ; 272: lib_System.Globalization.Extensions.dll.so => 41
	i64 u0x5cd9e6f9cf5c50e2, ; 273: lib_MUnique.OpenMU.Network.Packets.dll.so => 201
	i64 u0x5d0a4a29b02d9d3c, ; 274: System.Net.WebHeaderCollection.dll => 78
	i64 u0x5d7ec76c1c703055, ; 275: System.Threading.Tasks.Parallel => 144
	i64 u0x5db0cbbd1028510e, ; 276: lib_System.Runtime.InteropServices.dll.so => 108
	i64 u0x5e467bc8f09ad026, ; 277: System.Collections.Specialized.dll => 11
	i64 u0x5e5173b3208d97e7, ; 278: System.Runtime.Handles.dll => 105
	i64 u0x5ea92fdb19ec8c4c, ; 279: System.Text.Encodings.Web.dll => 137
	i64 u0x5eb8046dd40e9ac3, ; 280: System.ComponentModel.Primitives => 16
	i64 u0x5ec272d219c9aba4, ; 281: System.Security.Cryptography.Csp.dll => 122
	i64 u0x5eee1376d94c7f5e, ; 282: System.Net.HttpListener.dll => 66
	i64 u0x5f36ccf5c6a57e24, ; 283: System.Xml.ReaderWriter.dll => 157
	i64 u0x5f4294b9b63cb842, ; 284: System.Data.Common => 22
	i64 u0x5fac98e0b37a5b9d, ; 285: System.Runtime.CompilerServices.Unsafe.dll => 102
	i64 u0x609f4b7b63d802d4, ; 286: lib_Microsoft.Extensions.DependencyInjection.dll.so => 187
	i64 u0x60f62d786afcf130, ; 287: System.Memory => 63
	i64 u0x61bb78c89f867353, ; 288: System.IO => 58
	i64 u0x61d88f399afb2f45, ; 289: lib_System.Runtime.Loader.dll.so => 110
	i64 u0x622eef6f9e59068d, ; 290: System.Private.CoreLib => 173
	i64 u0x627f1e3758f21a31, ; 291: MUnique.OpenMU.PlugIns => 202
	i64 u0x63f1f6883c1e23c2, ; 292: lib_System.Collections.Immutable.dll.so => 9
	i64 u0x640e3b14dbd325c2, ; 293: System.Security.Cryptography.Algorithms.dll => 120
	i64 u0x64587004560099b9, ; 294: System.Reflection => 98
	i64 u0x64b1529a438a3c45, ; 295: lib_System.Runtime.Handles.dll.so => 105
	i64 u0x65d8ddec9a3de89e, ; 296: ru/Microsoft.CodeAnalysis.resources => 219
	i64 u0x65ece51227bfa724, ; 297: lib_System.Runtime.Numerics.dll.so => 111
	i64 u0x6679b2337ee6b22a, ; 298: lib_System.IO.FileSystem.Primitives.dll.so => 49
	i64 u0x6692e924eade1b29, ; 299: lib_System.Console.dll.so => 20
	i64 u0x66d6d6d889549963, ; 300: lib_Nito.AsyncEx.Tasks.dll.so => 204
	i64 u0x670eeabf01aaac92, ; 301: lib_Nito.Disposables.dll.so => 206
	i64 u0x674303f65d8fad6f, ; 302: lib_System.Net.Quic.dll.so => 72
	i64 u0x67c0802770244408, ; 303: System.Windows.dll => 155
	i64 u0x68100b69286e27cd, ; 304: lib_System.Formats.Tar.dll.so => 39
	i64 u0x6872ec7a2e36b1ac, ; 305: System.Drawing.Primitives.dll => 35
	i64 u0x68fbbbe2eb455198, ; 306: System.Formats.Asn1 => 38
	i64 u0x69c43767b6624bb2, ; 307: pl/Microsoft.CodeAnalysis.CSharp.resources => 230
	i64 u0x6a4d7577b2317255, ; 308: System.Runtime.InteropServices.dll => 108
	i64 u0x6abfbfb2796f4e84, ; 309: Microsoft.CodeAnalysis.CSharp => 181
	i64 u0x6afcedb171067e2b, ; 310: System.Core.dll => 21
	i64 u0x6bf011622a2bba12, ; 311: MUnique.OpenMU.Network.dll => 200
	i64 u0x6d365ea782169cda, ; 312: NLayer => 207
	i64 u0x6d70755158ca866e, ; 313: lib_System.ComponentModel.EventBasedAsync.dll.so => 15
	i64 u0x6d79993361e10ef2, ; 314: Microsoft.Extensions.Primitives => 198
	i64 u0x6d7eeca99577fc8b, ; 315: lib_System.Net.WebProxy.dll.so => 79
	i64 u0x6d8515b19946b6a2, ; 316: System.Net.WebProxy.dll => 79
	i64 u0x6d9bea6b3e895cf7, ; 317: Microsoft.Extensions.Primitives.dll => 198
	i64 u0x6e2fb2ace98ab808, ; 318: zh-Hant/Microsoft.CodeAnalysis.CSharp.resources => 235
	i64 u0x6e838d9a2a6f6c9e, ; 319: lib_System.ValueTuple.dll.so => 152
	i64 u0x6e9965ce1095e60a, ; 320: lib_System.Core.dll.so => 21
	i64 u0x6ffc4967cc47ba57, ; 321: System.IO.FileSystem.Watcher.dll => 50
	i64 u0x701cd46a1c25a5fe, ; 322: System.IO.FileSystem.dll => 51
	i64 u0x7078c940a89ab2ee, ; 323: ja/Microsoft.CodeAnalysis.CSharp.resources => 228
	i64 u0x71485e7ffdb4b958, ; 324: System.Reflection.Extensions => 94
	i64 u0x71ad672adbe48f35, ; 325: System.ComponentModel.Primitives.dll => 16
	i64 u0x724e6bfd5f7889f5, ; 326: lib_CommunityToolkit.HighPerformance.dll.so => 176
	i64 u0x725f5a9e82a45c81, ; 327: System.Security.Cryptography.Encoding => 123
	i64 u0x72e0300099accce1, ; 328: System.Xml.XPath.XDocument => 160
	i64 u0x730bfb248998f67a, ; 329: System.IO.Compression.ZipFile => 45
	i64 u0x73a6be34e822f9d1, ; 330: lib_System.Runtime.Serialization.dll.so => 116
	i64 u0x73e4ce94e2eb6ffc, ; 331: lib_System.Memory.dll.so => 63
	i64 u0x743a1eccf080489a, ; 332: WindowsBase.dll => 166
	i64 u0x755a91767330b3d4, ; 333: lib_Microsoft.Extensions.Configuration.dll.so => 182
	i64 u0x75c326eb821b85c4, ; 334: lib_System.ComponentModel.DataAnnotations.dll.so => 14
	i64 u0x76ca07b878f44da0, ; 335: System.Runtime.Numerics.dll => 111
	i64 u0x778a805e625329ef, ; 336: System.Linq.Parallel => 60
	i64 u0x779f67ad3b8efbd5, ; 337: Microsoft.Extensions.Configuration.Json.dll => 186
	i64 u0x77f8a4acc2fdc449, ; 338: System.Security.Cryptography.Cng.dll => 121
	i64 u0x782c5d8eb99ff201, ; 339: lib_Microsoft.VisualBasic.Core.dll.so => 2
	i64 u0x7888c8518f32343b, ; 340: tr/Microsoft.CodeAnalysis.resources.dll => 220
	i64 u0x7996e32deaf72986, ; 341: Microsoft.CodeAnalysis.CSharp.dll => 181
	i64 u0x7a822a70632bc814, ; 342: MuAndroid.dll => 0
	i64 u0x7a9a57d43b0845fa, ; 343: System.AppContext => 6
	i64 u0x7bef86a4335c4870, ; 344: System.ComponentModel.TypeConverter => 17
	i64 u0x7c41d387501568ba, ; 345: System.Net.WebClient.dll => 77
	i64 u0x7c8f4b4f3731320f, ; 346: Pipelines.Sockets.Unofficial.dll => 208
	i64 u0x7cd2ec8eaf5241cd, ; 347: System.Security.dll => 131
	i64 u0x7d2f64af74e3e118, ; 348: Nito.Disposables => 206
	i64 u0x7d4040680e64c3ea, ; 349: Pipelines.Sockets.Unofficial => 208
	i64 u0x7d8ee2bdc8e3aad1, ; 350: System.Numerics.Vectors => 83
	i64 u0x7dfc3d6d9d8d7b70, ; 351: System.Collections => 12
	i64 u0x7e2e564fa2f76c65, ; 352: lib_System.Diagnostics.Tracing.dll.so => 34
	i64 u0x7e302e110e1e1346, ; 353: lib_System.Security.Claims.dll.so => 119
	i64 u0x7e946809d6008ef2, ; 354: lib_System.ObjectModel.dll.so => 85
	i64 u0x7ecc13347c8fd849, ; 355: lib_System.ComponentModel.dll.so => 18
	i64 u0x7f9351cd44b1273f, ; 356: Microsoft.Extensions.Configuration.Abstractions => 183
	i64 u0x8076a9a44a2ca331, ; 357: System.Net.Quic => 72
	i64 u0x80da183a87731838, ; 358: System.Reflection.Metadata => 95
	i64 u0x80ee53ea610b3f78, ; 359: zh-Hans/Microsoft.CodeAnalysis.CSharp.resources => 234
	i64 u0x812c069d5cdecc17, ; 360: System.dll => 165
	i64 u0x81657cec2b31e8aa, ; 361: System.Net => 82
	i64 u0x82b399cb01b531c4, ; 362: lib_System.Web.dll.so => 154
	i64 u0x82df8f5532a10c59, ; 363: lib_System.Drawing.dll.so => 36
	i64 u0x82f0b6e911d13535, ; 364: lib_System.Transactions.dll.so => 151
	i64 u0x846ce984efea52c7, ; 365: System.Threading.Tasks.Parallel.dll => 144
	i64 u0x84ae73148a4557d2, ; 366: lib_System.IO.Pipes.dll.so => 56
	i64 u0x84b01102c12a9232, ; 367: System.Runtime.Serialization.Json.dll => 113
	i64 u0x8662aaeb94fef37f, ; 368: lib_System.Dynamic.Runtime.dll.so => 37
	i64 u0x86b3e00c36b84509, ; 369: Microsoft.Extensions.Configuration.dll => 182
	i64 u0x86b62cb077ec4fd7, ; 370: System.Runtime.Serialization.Xml => 115
	i64 u0x8704193f462e892e, ; 371: lib_Microsoft.Extensions.FileSystemGlobbing.dll.so => 191
	i64 u0x872a5b14c18d328c, ; 372: System.ComponentModel.DataAnnotations => 14
	i64 u0x87c69b87d9283884, ; 373: lib_System.Threading.Thread.dll.so => 146
	i64 u0x87f6569b25707834, ; 374: System.IO.Compression.Brotli.dll => 43
	i64 u0x88826e51a5d4a3d0, ; 375: de/Microsoft.CodeAnalysis.resources.dll => 211
	i64 u0x88ba6bc4f7762b03, ; 376: lib_System.Reflection.dll.so => 98
	i64 u0x8930322c7bd8f768, ; 377: netstandard => 168
	i64 u0x897a606c9e39c75f, ; 378: lib_System.ComponentModel.Primitives.dll.so => 16
	i64 u0x89911a22005b92b7, ; 379: System.IO.FileSystem.DriveInfo.dll => 48
	i64 u0x89c5188089ec2cd5, ; 380: lib_System.Runtime.InteropServices.RuntimeInformation.dll.so => 107
	i64 u0x8a19e3dc71b34b2c, ; 381: System.Reflection.TypeExtensions.dll => 97
	i64 u0x8b4ff5d0fdd5faa1, ; 382: lib_System.Diagnostics.DiagnosticSource.dll.so => 27
	i64 u0x8b541d476eb3774c, ; 383: System.Security.Principal.Windows => 128
	i64 u0x8b8d01333a96d0b5, ; 384: System.Diagnostics.Process.dll => 29
	i64 u0x8c39b02ed181787b, ; 385: pt-BR/Microsoft.CodeAnalysis.CSharp.resources => 231
	i64 u0x8c4e8fc055a910fb, ; 386: lib_SixLabors.ImageSharp.dll.so => 209
	i64 u0x8c575135aa1ccef4, ; 387: Microsoft.Extensions.FileProviders.Abstractions => 189
	i64 u0x8cdfdb4ce85fb925, ; 388: lib_System.Security.Principal.Windows.dll.so => 128
	i64 u0x8cdfe7b8f4caa426, ; 389: System.IO.Compression.FileSystem => 44
	i64 u0x8d7b8ab4b3310ead, ; 390: System.Threading => 149
	i64 u0x8da188285aadfe8e, ; 391: System.Collections.Concurrent => 8
	i64 u0x8f44b45eb046bbd1, ; 392: System.ServiceModel.Web.dll => 132
	i64 u0x8f8b0f07edd7b3b6, ; 393: cs/Microsoft.CodeAnalysis.resources.dll => 210
	i64 u0x8fa404e6277d0694, ; 394: zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll => 234
	i64 u0x8fbf5b0114c6dcef, ; 395: System.Globalization.dll => 42
	i64 u0x90263f8448b8f572, ; 396: lib_System.Diagnostics.TraceSource.dll.so => 33
	i64 u0x903101b46fb73a04, ; 397: _Microsoft.Android.Resource.Designer => 238
	i64 u0x90393bd4865292f3, ; 398: lib_System.IO.Compression.dll.so => 46
	i64 u0x905e2b8e7ae91ae6, ; 399: System.Threading.Tasks.Extensions.dll => 143
	i64 u0x90da922f4ccbc487, ; 400: lib_MonoGame.Framework.dll.so => 199
	i64 u0x90f95fc914407a17, ; 401: lib-pl-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 230
	i64 u0x914647982e998267, ; 402: Microsoft.Extensions.Configuration.Json => 186
	i64 u0x9157bd523cd7ed36, ; 403: lib_System.Text.Json.dll.so => 138
	i64 u0x91a74f07b30d37e2, ; 404: System.Linq.dll => 62
	i64 u0x91cb86ea3b17111d, ; 405: System.ServiceModel.Web => 132
	i64 u0x92054e486c0c7ea7, ; 406: System.IO.FileSystem.DriveInfo => 48
	i64 u0x926c3cf189fe2e18, ; 407: zh-Hans/Microsoft.CodeAnalysis.resources.dll => 221
	i64 u0x928614058c40c4cd, ; 408: lib_System.Xml.XPath.XDocument.dll.so => 160
	i64 u0x93ba953181e66fd2, ; 409: lib-ru-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 232
	i64 u0x93d7a094b35723f5, ; 410: Client.Main => 237
	i64 u0x944077d8ca3c6580, ; 411: System.IO.Compression.dll => 46
	i64 u0x948cffedc8ed7960, ; 412: System.Xml => 164
	i64 u0x9513edb9b5b8790f, ; 413: Nito.AsyncEx.Tasks.dll => 204
	i64 u0x9658628e1b5467be, ; 414: Nito.Collections.Deque => 205
	i64 u0x97b8c771ea3e4220, ; 415: System.ComponentModel.dll => 18
	i64 u0x97e144c9d3c6976e, ; 416: System.Collections.Concurrent.dll => 8
	i64 u0x97e62b58d5c7d5f9, ; 417: lib_Microsoft.Extensions.Logging.Console.dll.so => 195
	i64 u0x98270c46908e26f7, ; 418: zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll => 235
	i64 u0x98d720cc4597562c, ; 419: System.Security.Cryptography.OpenSsl => 124
	i64 u0x991d510397f92d9d, ; 420: System.Linq.Expressions => 59
	i64 u0x996ceeb8a3da3d67, ; 421: System.Threading.Overlapped.dll => 141
	i64 u0x99a891b860c3d03b, ; 422: lib-ko-Microsoft.CodeAnalysis.resources.dll.so => 216
	i64 u0x9a102e560c6efe86, ; 423: lib-pt-BR-Microsoft.CodeAnalysis.resources.dll.so => 218
	i64 u0x9b211a749105beac, ; 424: System.Transactions.Local => 150
	i64 u0x9b8734714671022d, ; 425: System.Threading.Tasks.Dataflow.dll => 142
	i64 u0x9ba8c32873c681c1, ; 426: it/Microsoft.CodeAnalysis.CSharp.resources.dll => 227
	i64 u0x9be4124ffc84e7ee, ; 427: pl/Microsoft.CodeAnalysis.resources.dll => 217
	i64 u0x9c244ac7cda32d26, ; 428: System.Security.Cryptography.X509Certificates.dll => 126
	i64 u0x9c69fdfa9a154b28, ; 429: tr/Microsoft.CodeAnalysis.CSharp.resources.dll => 233
	i64 u0x9c8f6872beab6408, ; 430: System.Xml.XPath.XDocument.dll => 160
	i64 u0x9ce01cf91101ae23, ; 431: System.Xml.XmlDocument => 162
	i64 u0x9d74dee1a7725f34, ; 432: Microsoft.Extensions.Configuration.Abstractions.dll => 183
	i64 u0x9dcb570d9792d506, ; 433: lib-ru-Microsoft.CodeAnalysis.resources.dll.so => 219
	i64 u0x9e4b95dec42769f7, ; 434: System.Diagnostics.Debug.dll => 26
	i64 u0x9e5a208afd9d15a6, ; 435: it/Microsoft.CodeAnalysis.CSharp.resources => 227
	i64 u0x9eeec7beda8fafa4, ; 436: BouncyCastle.Crypto.dll => 175
	i64 u0x9fbb2961ca18e5c2, ; 437: Microsoft.Extensions.FileProviders.Physical.dll => 190
	i64 u0xa00832eb975f56a8, ; 438: lib_System.Net.dll.so => 82
	i64 u0xa0d8259f4cc284ec, ; 439: lib_System.Security.Cryptography.dll.so => 127
	i64 u0xa0ff9b3e34d92f11, ; 440: lib_System.Resources.Writer.dll.so => 101
	i64 u0xa12fbfb4da97d9f3, ; 441: System.Threading.Timer.dll => 148
	i64 u0xa2572680829d2c7c, ; 442: System.IO.Pipelines.dll => 54
	i64 u0xa26597e57ee9c7f6, ; 443: System.Xml.XmlDocument.dll => 162
	i64 u0xa308401900e5bed3, ; 444: lib_mscorlib.dll.so => 167
	i64 u0xa3884718456d17d1, ; 445: lib_INIFileParser.dll.so => 178
	i64 u0xa395572e7da6c99d, ; 446: lib_System.Security.dll.so => 131
	i64 u0xa3d089b150e18d27, ; 447: pt-BR/Microsoft.CodeAnalysis.resources.dll => 218
	i64 u0xa3e683f24b43af6f, ; 448: System.Dynamic.Runtime.dll => 37
	i64 u0xa4edc8f2ceae241a, ; 449: System.Data.Common.dll => 22
	i64 u0xa5494f40f128ce6a, ; 450: System.Runtime.Serialization.Formatters.dll => 112
	i64 u0xa54b74df83dce92b, ; 451: System.Reflection.DispatchProxy => 90
	i64 u0xa5b7152421ed6d98, ; 452: lib_System.IO.FileSystem.Watcher.dll.so => 50
	i64 u0xa5c3844f17b822db, ; 453: lib_System.Linq.Parallel.dll.so => 60
	i64 u0xa5ce5c755bde8cb8, ; 454: lib_System.Security.Cryptography.Csp.dll.so => 122
	i64 u0xa5e599d1e0524750, ; 455: System.Numerics.Vectors.dll => 83
	i64 u0xa5f1ba49b85dd355, ; 456: System.Security.Cryptography.dll => 127
	i64 u0xa61975a5a37873ea, ; 457: lib_System.Xml.XmlSerializer.dll.so => 163
	i64 u0xa66cbee0130865f7, ; 458: lib_WindowsBase.dll.so => 166
	i64 u0xa763fbb98df8d9fb, ; 459: lib_Microsoft.Win32.Primitives.dll.so => 4
	i64 u0xa7eab29ed44b4e7a, ; 460: Mono.Android.Export => 170
	i64 u0xa8195217cbf017b7, ; 461: Microsoft.VisualBasic.Core => 2
	i64 u0xa82fd211eef00a5b, ; 462: Microsoft.Extensions.FileProviders.Physical => 190
	i64 u0xa8adea9b1f260c23, ; 463: lib-it-Microsoft.CodeAnalysis.resources.dll.so => 214
	i64 u0xa8b52f21e0dbe690, ; 464: System.Runtime.Serialization.dll => 116
	i64 u0xa95590e7c57438a4, ; 465: System.Configuration => 19
	i64 u0xaa2219c8e3449ff5, ; 466: Microsoft.Extensions.Logging.Abstractions => 193
	i64 u0xaa443ac34067eeef, ; 467: System.Private.Xml.dll => 89
	i64 u0xaa52de307ef5d1dd, ; 468: System.Net.Http => 65
	i64 u0xaa9a7b0214a5cc5c, ; 469: System.Diagnostics.StackTrace.dll => 30
	i64 u0xaaaf86367285a918, ; 470: Microsoft.Extensions.DependencyInjection.Abstractions.dll => 188
	i64 u0xaae72bd80754669a, ; 471: lib-es-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 225
	i64 u0xab9c1b2687d86b0b, ; 472: lib_System.Linq.Expressions.dll.so => 59
	i64 u0xac2af3fa195a15ce, ; 473: System.Runtime.Numerics => 111
	i64 u0xac5acae88f60357e, ; 474: System.Diagnostics.Tools.dll => 32
	i64 u0xac79c7e46047ad98, ; 475: System.Security.Principal.Windows.dll => 128
	i64 u0xac98d31068e24591, ; 476: System.Xml.XDocument => 159
	i64 u0xacf42eea7ef9cd12, ; 477: System.Threading.Channels => 140
	i64 u0xadbb53caf78a79d2, ; 478: System.Web.HttpUtility => 153
	i64 u0xadc90ab061a9e6e4, ; 479: System.ComponentModel.TypeConverter.dll => 17
	i64 u0xadf4cf30debbeb9a, ; 480: System.Net.ServicePoint.dll => 75
	i64 u0xadf511667bef3595, ; 481: System.Net.Security => 74
	i64 u0xae0aaa94fdcfce0f, ; 482: System.ComponentModel.EventBasedAsync.dll => 15
	i64 u0xae282bcd03739de7, ; 483: Java.Interop => 169
	i64 u0xae53579c90db1107, ; 484: System.ObjectModel.dll => 85
	i64 u0xaeafff290ccb288d, ; 485: cs/Microsoft.CodeAnalysis.CSharp.resources => 223
	i64 u0xaf1fc169e9288b46, ; 486: lib_Nito.Collections.Deque.dll.so => 205
	i64 u0xaf30435f69f89569, ; 487: lib_Delizious.Ini.dll.so => 177
	i64 u0xaf732d0b2193b8f5, ; 488: System.Security.Cryptography.OpenSsl.dll => 124
	i64 u0xb0a6fac2b157ab4d, ; 489: Delizious.Ini => 177
	i64 u0xb0bb43dc52ea59f9, ; 490: System.Diagnostics.Tracing.dll => 34
	i64 u0xb0c6678edfb08a6d, ; 491: lib-es-Microsoft.CodeAnalysis.resources.dll.so => 212
	i64 u0xb1dd05401aa8ee63, ; 492: System.Security.AccessControl => 118
	i64 u0xb220631954820169, ; 493: System.Text.RegularExpressions => 139
	i64 u0xb236a5c1d686880f, ; 494: INIFileParser => 178
	i64 u0xb2376e1dbf8b4ed7, ; 495: System.Security.Cryptography.Csp => 122
	i64 u0xb2a1959fe95c5402, ; 496: lib_System.Runtime.InteropServices.JavaScript.dll.so => 106
	i64 u0xb3c26669ed699377, ; 497: lib_MUnique.OpenMU.PlugIns.dll.so => 202
	i64 u0xb3d5b1cf730ea936, ; 498: pt-BR/Microsoft.CodeAnalysis.resources => 218
	i64 u0xb40a1aa1ed99a237, ; 499: INIFileParser.dll => 178
	i64 u0xb4b3092fd37a579a, ; 500: ja/Microsoft.CodeAnalysis.CSharp.resources.dll => 228
	i64 u0xb4bd7015ecee9d86, ; 501: System.IO.Pipelines => 54
	i64 u0xb4c53d9749c5f226, ; 502: lib_System.IO.FileSystem.AccessControl.dll.so => 47
	i64 u0xb4ff710863453fda, ; 503: System.Diagnostics.FileVersionInfo.dll => 28
	i64 u0xb5c38bf497a4cfe2, ; 504: lib_System.Threading.Tasks.dll.so => 145
	i64 u0xb5c7fcdafbc67ee4, ; 505: Microsoft.Extensions.Logging.Abstractions.dll => 193
	i64 u0xb5ea31d5244c6626, ; 506: System.Threading.ThreadPool.dll => 147
	i64 u0xb6daa312e893d3c4, ; 507: lib-ja-Microsoft.CodeAnalysis.resources.dll.so => 215
	i64 u0xb6e7360af04a7cae, ; 508: lib_MUnique.OpenMU.Network.dll.so => 200
	i64 u0xb7212c4683a94afe, ; 509: System.Drawing.Primitives => 35
	i64 u0xb81a2c6e0aee50fe, ; 510: lib_System.Private.CoreLib.dll.so => 173
	i64 u0xb8c60af47c08d4da, ; 511: System.Net.ServicePoint => 75
	i64 u0xb8e68d20aad91196, ; 512: lib_System.Xml.XPath.dll.so => 161
	i64 u0xb9185c33a1643eed, ; 513: Microsoft.CSharp.dll => 1
	i64 u0xba4670aa94a2b3c6, ; 514: lib_System.Xml.XDocument.dll.so => 159
	i64 u0xba48785529705af9, ; 515: System.Collections.dll => 12
	i64 u0xba965b8c86359996, ; 516: lib_System.Windows.dll.so => 155
	i64 u0xbb286883bc35db36, ; 517: System.Transactions.dll => 151
	i64 u0xbb65706fde942ce3, ; 518: System.Net.Sockets => 76
	i64 u0xbb822a624c99bd72, ; 519: lib-zh-Hans-Microsoft.CodeAnalysis.resources.dll.so => 221
	i64 u0xbba28979413cad9e, ; 520: lib_System.Runtime.CompilerServices.VisualC.dll.so => 103
	i64 u0xbbd180354b67271a, ; 521: System.Runtime.Serialization.Formatters => 112
	i64 u0xbc0ad520c3be6d31, ; 522: ja/Microsoft.CodeAnalysis.resources => 215
	i64 u0xbc0f18bd5e853386, ; 523: Delizious.Ini.dll => 177
	i64 u0xbd0e2c0d55246576, ; 524: System.Net.Http.dll => 65
	i64 u0xbd3fbd85b9e1cb29, ; 525: lib_System.Net.HttpListener.dll.so => 66
	i64 u0xbd4f572d2bd0a789, ; 526: System.IO.Compression.ZipFile.dll => 45
	i64 u0xbd770a375f100c23, ; 527: lib_Pipelines.Sockets.Unofficial.dll.so => 208
	i64 u0xbd877b14d0b56392, ; 528: System.Runtime.Intrinsics.dll => 109
	i64 u0xbe04c77645f06233, ; 529: Microsoft.Extensions.Logging.Configuration => 194
	i64 u0xbe65a49036345cf4, ; 530: lib_System.Buffers.dll.so => 7
	i64 u0xbef9919db45b4ca7, ; 531: System.IO.Pipes.AccessControl => 55
	i64 u0xbfc1e1fb3095f2b3, ; 532: lib_System.Net.Http.Json.dll.so => 64
	i64 u0xbfd57e7eba42c6c7, ; 533: de/Microsoft.CodeAnalysis.CSharp.resources.dll => 224
	i64 u0xc0d928351ab5ca77, ; 534: System.Console.dll => 20
	i64 u0xc0f5a221a9383aea, ; 535: System.Runtime.Intrinsics => 109
	i64 u0xc111030af54d7191, ; 536: System.Resources.Writer => 101
	i64 u0xc12b8b3afa48329c, ; 537: lib_System.Linq.dll.so => 62
	i64 u0xc183ca0b74453aa9, ; 538: lib_System.Threading.Tasks.Dataflow.dll.so => 142
	i64 u0xc1afcc0a4309f4e3, ; 539: ko/Microsoft.CodeAnalysis.resources.dll => 216
	i64 u0xc26c064effb1dea9, ; 540: System.Buffers.dll => 7
	i64 u0xc2902f6cf5452577, ; 541: lib_Mono.Android.Export.dll.so => 170
	i64 u0xc2a3bca55b573141, ; 542: System.IO.FileSystem.Watcher => 50
	i64 u0xc2d6a22447844c42, ; 543: MUnique.OpenMU.PlugIns.dll => 202
	i64 u0xc30b52815b58ac2c, ; 544: lib_System.Runtime.Serialization.Xml.dll.so => 115
	i64 u0xc36d7d89c652f455, ; 545: System.Threading.Overlapped => 141
	i64 u0xc3c86c1e5e12f03d, ; 546: WindowsBase => 166
	i64 u0xc3e74964279d65e6, ; 547: zh-Hans/Microsoft.CodeAnalysis.resources => 221
	i64 u0xc421b61fd853169d, ; 548: lib_System.Net.WebSockets.Client.dll.so => 80
	i64 u0xc463e077917aa21d, ; 549: System.Runtime.Serialization.Json => 113
	i64 u0xc4e10c07e81c1c6a, ; 550: Client.Main.dll => 237
	i64 u0xc50fded0ded1418c, ; 551: lib_System.ComponentModel.TypeConverter.dll.so => 17
	i64 u0xc519125d6bc8fb11, ; 552: lib_System.Net.Requests.dll.so => 73
	i64 u0xc5325b2fcb37446f, ; 553: lib_System.Private.Xml.dll.so => 89
	i64 u0xc5a0f4b95a699af7, ; 554: lib_System.Private.Uri.dll.so => 87
	i64 u0xc5cdcd5b6277579e, ; 555: lib_System.Security.Cryptography.Algorithms.dll.so => 120
	i64 u0xc659b586d4c229e2, ; 556: Microsoft.Extensions.Configuration.FileExtensions.dll => 185
	i64 u0xc7c01e7d7c93a110, ; 557: System.Text.Encoding.Extensions.dll => 135
	i64 u0xc7ce851898a4548e, ; 558: lib_System.Web.HttpUtility.dll.so => 153
	i64 u0xc809d4089d2556b2, ; 559: System.Runtime.InteropServices.JavaScript.dll => 106
	i64 u0xc858a28d9ee5a6c5, ; 560: lib_System.Collections.Specialized.dll.so => 11
	i64 u0xc8ac7c6bf1c2ec51, ; 561: System.Reflection.DispatchProxy.dll => 90
	i64 u0xc9c62c8f354ac568, ; 562: lib_System.Diagnostics.TextWriterTraceListener.dll.so => 31
	i64 u0xca5801070d9fccfb, ; 563: System.Text.Encoding => 136
	i64 u0xcbb5f80c7293e696, ; 564: lib_System.Globalization.Calendars.dll.so => 40
	i64 u0xcbd4fdd9cef4a294, ; 565: lib__Microsoft.Android.Resource.Designer.dll.so => 238
	i64 u0xcc2876b32ef2794c, ; 566: lib_System.Text.RegularExpressions.dll.so => 139
	i64 u0xcc667334debe5bbf, ; 567: MonoGame.Framework.dll => 199
	i64 u0xcc9fa2923aa1c9ef, ; 568: System.Diagnostics.Contracts.dll => 25
	i64 u0xcd10a42808629144, ; 569: System.Net.Requests => 73
	i64 u0xcf23d8093f3ceadf, ; 570: System.Diagnostics.DiagnosticSource.dll => 27
	i64 u0xcf3a9996a625b6e2, ; 571: Microsoft.Extensions.Logging.Configuration.dll => 194
	i64 u0xcf5ff6b6b2c4c382, ; 572: System.Net.Mail.dll => 67
	i64 u0xcf8fc898f98b0d34, ; 573: System.Private.Xml.Linq => 88
	i64 u0xd04b5f59ed596e31, ; 574: System.Reflection.Metadata.dll => 95
	i64 u0xd063299fcfc0c93f, ; 575: lib_System.Runtime.Serialization.Json.dll.so => 113
	i64 u0xd0de8a113e976700, ; 576: System.Diagnostics.TextWriterTraceListener => 31
	i64 u0xd0fc33d5ae5d4cb8, ; 577: System.Runtime.Extensions => 104
	i64 u0xd118cf03aa687fdf, ; 578: cs/Microsoft.CodeAnalysis.resources => 210
	i64 u0xd12beacdfc14f696, ; 579: System.Dynamic.Runtime => 37
	i64 u0xd198e7ce1b6a8344, ; 580: System.Net.Quic.dll => 72
	i64 u0xd333d0af9e423810, ; 581: System.Runtime.InteropServices => 108
	i64 u0xd33a415cb4278969, ; 582: System.Security.Cryptography.Encoding.dll => 123
	i64 u0xd3651b6fc3125825, ; 583: System.Private.Uri.dll => 87
	i64 u0xd373685349b1fe8b, ; 584: Microsoft.Extensions.Logging.dll => 192
	i64 u0xd3801faafafb7698, ; 585: System.Private.DataContractSerialization.dll => 86
	i64 u0xd3edcc1f25459a50, ; 586: System.Reflection.Emit => 93
	i64 u0xd3fcd093baced313, ; 587: Nito.Disposables.dll => 206
	i64 u0xd4645626dffec99d, ; 588: lib_Microsoft.Extensions.DependencyInjection.Abstractions.dll.so => 188
	i64 u0xd4fa0abb79079ea9, ; 589: System.Security.Principal.dll => 129
	i64 u0xd6f8b724c5400fc8, ; 590: BouncyCastle.Crypto => 175
	i64 u0xd72329819cbbbc44, ; 591: lib_Microsoft.Extensions.Configuration.Abstractions.dll.so => 183
	i64 u0xd72c760af136e863, ; 592: System.Xml.XmlSerializer.dll => 163
	i64 u0xd753f071e44c2a03, ; 593: lib_System.Security.SecureString.dll.so => 130
	i64 u0xd7b3764ada9d341d, ; 594: lib_Microsoft.Extensions.Logging.Abstractions.dll.so => 193
	i64 u0xd7f3c8662df47acb, ; 595: MUnique.OpenMU.Network.Packets => 201
	i64 u0xda1dfa4c534a9251, ; 596: Microsoft.Extensions.DependencyInjection => 187
	i64 u0xdad05a11827959a3, ; 597: System.Collections.NonGeneric.dll => 10
	i64 u0xdaefdfe71aa53cf9, ; 598: System.IO.FileSystem.Primitives => 49
	i64 u0xdb329bf10f3df393, ; 599: BCnEncoder.dll => 174
	i64 u0xdb58816721c02a59, ; 600: lib_System.Reflection.Emit.ILGeneration.dll.so => 91
	i64 u0xdbf2a779fbc3ac31, ; 601: System.Transactions.Local.dll => 150
	i64 u0xdbf9607a441b4505, ; 602: System.Linq => 62
	i64 u0xdbfc90157a0de9b0, ; 603: lib_System.Text.Encoding.dll.so => 136
	i64 u0xdc4026301e09de53, ; 604: lib_Client.Data.dll.so => 236
	i64 u0xdc75032002d1a212, ; 605: lib_System.Transactions.Local.dll.so => 150
	i64 u0xdca8be7403f92d4f, ; 606: lib_System.Linq.Queryable.dll.so => 61
	i64 u0xdcbf1e32b739302e, ; 607: de/Microsoft.CodeAnalysis.resources => 211
	i64 u0xdce2c53525640bf3, ; 608: Microsoft.Extensions.Logging => 192
	i64 u0xdd14049e4243731e, ; 609: lib-it-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 227
	i64 u0xdd2b722d78ef5f43, ; 610: System.Runtime.dll => 117
	i64 u0xdd67031857c72f96, ; 611: lib_System.Text.Encodings.Web.dll.so => 137
	i64 u0xdd92e229ad292030, ; 612: System.Numerics.dll => 84
	i64 u0xde110ae80fa7c2e2, ; 613: System.Xml.XDocument.dll => 159
	i64 u0xde569790b5833312, ; 614: lib_MuAndroid.dll.so => 0
	i64 u0xde572c2b2fb32f93, ; 615: lib_System.Threading.Tasks.Extensions.dll.so => 143
	i64 u0xdf4b773de8fb1540, ; 616: System.Net.dll => 82
	i64 u0xdfa254ebb4346068, ; 617: System.Net.Ping => 70
	i64 u0xe021eaa401792a05, ; 618: System.Text.Encoding.dll => 136
	i64 u0xe10b760bb1462e7a, ; 619: lib_System.Security.Cryptography.Primitives.dll.so => 125
	i64 u0xe192a588d4410686, ; 620: lib_System.IO.Pipelines.dll.so => 54
	i64 u0xe1a08bd3fa539e0d, ; 621: System.Runtime.Loader => 110
	i64 u0xe1a77eb8831f7741, ; 622: System.Security.SecureString.dll => 130
	i64 u0xe1b52f9f816c70ef, ; 623: System.Private.Xml.Linq.dll => 88
	i64 u0xe1e199c8ab02e356, ; 624: System.Data.DataSetExtensions.dll => 23
	i64 u0xe1e852de9692e4b8, ; 625: es/Microsoft.CodeAnalysis.CSharp.resources => 225
	i64 u0xe1ecfdb7fff86067, ; 626: System.Net.Security.dll => 74
	i64 u0xe2252a80fe853de4, ; 627: lib_System.Security.Principal.dll.so => 129
	i64 u0xe22fa4c9c645db62, ; 628: System.Diagnostics.TextWriterTraceListener.dll => 31
	i64 u0xe2420585aeceb728, ; 629: System.Net.Requests.dll => 73
	i64 u0xe274fda7b2443f04, ; 630: lib_Nito.AsyncEx.Coordination.dll.so => 203
	i64 u0xe2ad448dee50fbdf, ; 631: System.Xml.Serialization => 158
	i64 u0xe2d920f978f5d85c, ; 632: System.Data.DataSetExtensions => 23
	i64 u0xe2e426c7714fa0bc, ; 633: Microsoft.Win32.Primitives.dll => 4
	i64 u0xe332bacb3eb4a806, ; 634: Mono.Android.Export.dll => 170
	i64 u0xe3b7cbae5ad66c75, ; 635: lib_System.Security.Cryptography.Encoding.dll.so => 123
	i64 u0xe4f74a0b5bf9703f, ; 636: System.Runtime.Serialization.Primitives => 114
	i64 u0xe51aadb833ed7eb1, ; 637: lib_Microsoft.CodeAnalysis.CSharp.dll.so => 181
	i64 u0xe529964b351f8a52, ; 638: pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll => 231
	i64 u0xe5434e8a119ceb69, ; 639: lib_Mono.Android.dll.so => 172
	i64 u0xe55703b9ce5c038a, ; 640: System.Diagnostics.Tools => 32
	i64 u0xe57013c8afc270b5, ; 641: Microsoft.VisualBasic => 3
	i64 u0xe62913cc36bc07ec, ; 642: System.Xml.dll => 164
	i64 u0xe7b916eaefda3b00, ; 643: fr/Microsoft.CodeAnalysis.resources.dll => 213
	i64 u0xe7dd1e7ea292e8bc, ; 644: ko/Microsoft.CodeAnalysis.resources => 216
	i64 u0xe7e03cc18dcdeb49, ; 645: lib_System.Diagnostics.StackTrace.dll.so => 30
	i64 u0xe7e147ff99a7a380, ; 646: lib_System.Configuration.dll.so => 19
	i64 u0xe8397cf3948e7cb7, ; 647: lib_Microsoft.Extensions.Options.ConfigurationExtensions.dll.so => 197
	i64 u0xe896622fe0902957, ; 648: System.Reflection.Emit.dll => 93
	i64 u0xe89a2a9ef110899b, ; 649: System.Drawing.dll => 36
	i64 u0xe8c5f8c100b5934b, ; 650: Microsoft.Win32.Registry => 5
	i64 u0xe9b9c8c0458fd92a, ; 651: System.Windows => 155
	i64 u0xec7ff6499180295b, ; 652: Nito.AsyncEx.Coordination.dll => 203
	i64 u0xedc4817167106c23, ; 653: System.Net.Sockets.dll => 76
	i64 u0xedc632067fb20ff3, ; 654: System.Memory.dll => 63
	i64 u0xee81f5b3f1c4f83b, ; 655: System.Threading.ThreadPool => 147
	i64 u0xeeb091ae7b5bb4bd, ; 656: CommunityToolkit.HighPerformance.dll => 176
	i64 u0xeefc635595ef57f0, ; 657: System.Security.Cryptography.Cng => 121
	i64 u0xef03b1b5a04e9709, ; 658: System.Text.Encoding.CodePages.dll => 134
	i64 u0xefb4a748df9eaf75, ; 659: lib_Client.Main.dll.so => 237
	i64 u0xefd1e0c4e5c9b371, ; 660: System.Resources.ResourceManager.dll => 100
	i64 u0xefe8f8d5ed3c72ea, ; 661: System.Formats.Tar.dll => 39
	i64 u0xf09e47b6ae914f6e, ; 662: System.Net.NameResolution => 68
	i64 u0xf0ac2b489fed2e35, ; 663: lib_System.Diagnostics.Debug.dll.so => 26
	i64 u0xf0bb49dadd3a1fe1, ; 664: lib_System.Net.ServicePoint.dll.so => 75
	i64 u0xf0de2537ee19c6ca, ; 665: lib_System.Net.WebHeaderCollection.dll.so => 78
	i64 u0xf161f4f3c3b7e62c, ; 666: System.Data => 24
	i64 u0xf16eb650d5a464bc, ; 667: System.ValueTuple => 152
	i64 u0xf1c4b4005493d871, ; 668: System.Formats.Asn1.dll => 38
	i64 u0xf27ac96c4a2c11ce, ; 669: lib-fr-Microsoft.CodeAnalysis.resources.dll.so => 213
	i64 u0xf300e085f8acd238, ; 670: lib_System.ServiceProcess.dll.so => 133
	i64 u0xf34e52b26e7e059d, ; 671: System.Runtime.CompilerServices.VisualC.dll => 103
	i64 u0xf3ad9b8fb3eefd12, ; 672: lib_System.IO.UnmanagedMemoryStream.dll.so => 57
	i64 u0xf3ddfe05336abf29, ; 673: System => 165
	i64 u0xf408654b2a135055, ; 674: System.Reflection.Emit.ILGeneration.dll => 91
	i64 u0xf4103170a1de5bd0, ; 675: System.Linq.Queryable.dll => 61
	i64 u0xf41b241c82f75cde, ; 676: ru/Microsoft.CodeAnalysis.CSharp.resources.dll => 232
	i64 u0xf41eebf9fb91e2a1, ; 677: it/Microsoft.CodeAnalysis.resources.dll => 214
	i64 u0xf42d20c23173d77c, ; 678: lib_System.ServiceModel.Web.dll.so => 132
	i64 u0xf4c1dd70a5496a17, ; 679: System.IO.Compression => 46
	i64 u0xf4ecf4b9afc64781, ; 680: System.ServiceProcess.dll => 133
	i64 u0xf518f63ead11fcd1, ; 681: System.Threading.Tasks => 145
	i64 u0xf5967aac376787d7, ; 682: Microsoft.CodeAnalysis.dll => 180
	i64 u0xf5fc7602fe27b333, ; 683: System.Net.WebHeaderCollection => 78
	i64 u0xf6d3c99924aa71a7, ; 684: MUnique.OpenMU.Network.Packets.dll => 201
	i64 u0xf6de7fa3776f8927, ; 685: lib_Microsoft.Extensions.Configuration.Json.dll.so => 186
	i64 u0xf6f893f692f8cb43, ; 686: Microsoft.Extensions.Options.ConfigurationExtensions.dll => 197
	i64 u0xf70c0a7bf8ccf5af, ; 687: System.Web => 154
	i64 u0xf7e2cac4c45067b3, ; 688: lib_System.Numerics.Vectors.dll.so => 83
	i64 u0xf8aac5ea82de1348, ; 689: System.Linq.Queryable => 61
	i64 u0xf8b77539b362d3ba, ; 690: lib_System.Reflection.Primitives.dll.so => 96
	i64 u0xf915dc29808193a1, ; 691: System.Web.HttpUtility.dll => 153
	i64 u0xf9be54c8bcf8ff3b, ; 692: System.Security.AccessControl.dll => 118
	i64 u0xf9eec5bb3a6aedc6, ; 693: Microsoft.Extensions.Options => 196
	i64 u0xfa0e82300e67f913, ; 694: lib_System.AppContext.dll.so => 6
	i64 u0xfa2fdb27e8a2c8e8, ; 695: System.ComponentModel.EventBasedAsync => 15
	i64 u0xfa3f278f288b0e84, ; 696: lib_System.Net.Security.dll.so => 74
	i64 u0xfa504dfa0f097d72, ; 697: Microsoft.Extensions.FileProviders.Abstractions.dll => 189
	i64 u0xfa645d91e9fc4cba, ; 698: System.Threading.Thread => 146
	i64 u0xfa767e5b5a39487b, ; 699: lib_BCnEncoder.dll.so => 174
	i64 u0xfad4d2c770e827f9, ; 700: lib_System.IO.IsolatedStorage.dll.so => 52
	i64 u0xfb06dd2338e6f7c4, ; 701: System.Net.Ping.dll => 70
	i64 u0xfb087abe5365e3b7, ; 702: lib_System.Data.DataSetExtensions.dll.so => 23
	i64 u0xfb846e949baff5ea, ; 703: System.Xml.Serialization.dll => 158
	i64 u0xfbad3e4ce4b98145, ; 704: System.Security.Cryptography.X509Certificates => 126
	i64 u0xfbf0a31c9fc34bc4, ; 705: lib_System.Net.Http.dll.so => 65
	i64 u0xfc6b7527cc280b3f, ; 706: lib_System.Runtime.Serialization.Formatters.dll.so => 112
	i64 u0xfc93fc307d279893, ; 707: System.IO.Pipes.AccessControl.dll => 55
	i64 u0xfcd302092ada6328, ; 708: System.IO.MemoryMappedFiles.dll => 53
	i64 u0xfd49b3c1a76e2748, ; 709: System.Runtime.InteropServices.RuntimeInformation => 107
	i64 u0xfd536c702f64dc47, ; 710: System.Text.Encoding.Extensions => 135
	i64 u0xfda36abccf05cf5c, ; 711: System.Net.WebSockets.Client => 80
	i64 u0xfe9856c3af9365ab, ; 712: lib_Microsoft.Extensions.Configuration.FileExtensions.dll.so => 185
	i64 u0xfec8e01187d0178c, ; 713: lib-ja-Microsoft.CodeAnalysis.CSharp.resources.dll.so => 228
	i64 u0xff270a55858bac8d, ; 714: System.Security.Principal => 129
	i64 u0xff9b54613e0d2cc8, ; 715: System.Net.Http.Json => 64
	i64 u0xffdb7a971be4ec73 ; 716: System.ValueTuple.dll => 152
], align 8

@assembly_image_cache_indices = dso_local local_unnamed_addr constant [717 x i32] [
	i32 42, i32 13, i32 197, i32 105, i32 171, i32 48, i32 7, i32 86,
	i32 71, i32 12, i32 102, i32 156, i32 19, i32 161, i32 167, i32 10,
	i32 96, i32 13, i32 196, i32 10, i32 127, i32 95, i32 140, i32 39,
	i32 195, i32 172, i32 5, i32 67, i32 222, i32 130, i32 68, i32 214,
	i32 211, i32 66, i32 57, i32 180, i32 52, i32 180, i32 43, i32 125,
	i32 200, i32 232, i32 67, i32 81, i32 229, i32 158, i32 92, i32 99,
	i32 141, i32 224, i32 151, i32 222, i32 162, i32 169, i32 188, i32 229,
	i32 81, i32 233, i32 4, i32 5, i32 51, i32 101, i32 56, i32 175,
	i32 120, i32 98, i32 168, i32 118, i32 21, i32 137, i32 97, i32 223,
	i32 77, i32 222, i32 119, i32 215, i32 8, i32 225, i32 165, i32 70,
	i32 217, i32 190, i32 171, i32 145, i32 40, i32 47, i32 30, i32 144,
	i32 196, i32 163, i32 28, i32 84, i32 77, i32 43, i32 29, i32 42,
	i32 103, i32 117, i32 45, i32 91, i32 56, i32 148, i32 146, i32 100,
	i32 49, i32 20, i32 236, i32 219, i32 114, i32 198, i32 94, i32 58,
	i32 220, i32 81, i32 169, i32 26, i32 71, i32 69, i32 33, i32 14,
	i32 139, i32 38, i32 191, i32 209, i32 194, i32 134, i32 92, i32 88,
	i32 149, i32 24, i32 138, i32 57, i32 217, i32 51, i32 29, i32 157,
	i32 34, i32 164, i32 52, i32 238, i32 90, i32 35, i32 210, i32 157,
	i32 191, i32 9, i32 76, i32 55, i32 230, i32 13, i32 182, i32 109,
	i32 226, i32 32, i32 104, i32 84, i32 92, i32 53, i32 204, i32 96,
	i32 58, i32 179, i32 9, i32 102, i32 68, i32 189, i32 125, i32 116,
	i32 135, i32 126, i32 106, i32 131, i32 147, i32 156, i32 185, i32 179,
	i32 97, i32 24, i32 143, i32 3, i32 212, i32 167, i32 176, i32 100,
	i32 161, i32 99, i32 25, i32 93, i32 168, i32 172, i32 3, i32 195,
	i32 1, i32 114, i32 33, i32 6, i32 0, i32 207, i32 209, i32 156,
	i32 235, i32 53, i32 199, i32 203, i32 85, i32 213, i32 44, i32 104,
	i32 234, i32 224, i32 47, i32 138, i32 205, i32 236, i32 64, i32 69,
	i32 80, i32 59, i32 89, i32 154, i32 133, i32 110, i32 171, i32 134,
	i32 223, i32 140, i32 40, i32 184, i32 60, i32 184, i32 79, i32 25,
	i32 36, i32 99, i32 71, i32 22, i32 229, i32 121, i32 69, i32 107,
	i32 119, i32 117, i32 226, i32 11, i32 2, i32 124, i32 115, i32 142,
	i32 179, i32 41, i32 87, i32 231, i32 173, i32 27, i32 174, i32 148,
	i32 184, i32 207, i32 187, i32 226, i32 1, i32 44, i32 149, i32 18,
	i32 86, i32 41, i32 212, i32 233, i32 94, i32 192, i32 28, i32 220,
	i32 41, i32 201, i32 78, i32 144, i32 108, i32 11, i32 105, i32 137,
	i32 16, i32 122, i32 66, i32 157, i32 22, i32 102, i32 187, i32 63,
	i32 58, i32 110, i32 173, i32 202, i32 9, i32 120, i32 98, i32 105,
	i32 219, i32 111, i32 49, i32 20, i32 204, i32 206, i32 72, i32 155,
	i32 39, i32 35, i32 38, i32 230, i32 108, i32 181, i32 21, i32 200,
	i32 207, i32 15, i32 198, i32 79, i32 79, i32 198, i32 235, i32 152,
	i32 21, i32 50, i32 51, i32 228, i32 94, i32 16, i32 176, i32 123,
	i32 160, i32 45, i32 116, i32 63, i32 166, i32 182, i32 14, i32 111,
	i32 60, i32 186, i32 121, i32 2, i32 220, i32 181, i32 0, i32 6,
	i32 17, i32 77, i32 208, i32 131, i32 206, i32 208, i32 83, i32 12,
	i32 34, i32 119, i32 85, i32 18, i32 183, i32 72, i32 95, i32 234,
	i32 165, i32 82, i32 154, i32 36, i32 151, i32 144, i32 56, i32 113,
	i32 37, i32 182, i32 115, i32 191, i32 14, i32 146, i32 43, i32 211,
	i32 98, i32 168, i32 16, i32 48, i32 107, i32 97, i32 27, i32 128,
	i32 29, i32 231, i32 209, i32 189, i32 128, i32 44, i32 149, i32 8,
	i32 132, i32 210, i32 234, i32 42, i32 33, i32 238, i32 46, i32 143,
	i32 199, i32 230, i32 186, i32 138, i32 62, i32 132, i32 48, i32 221,
	i32 160, i32 232, i32 237, i32 46, i32 164, i32 204, i32 205, i32 18,
	i32 8, i32 195, i32 235, i32 124, i32 59, i32 141, i32 216, i32 218,
	i32 150, i32 142, i32 227, i32 217, i32 126, i32 233, i32 160, i32 162,
	i32 183, i32 219, i32 26, i32 227, i32 175, i32 190, i32 82, i32 127,
	i32 101, i32 148, i32 54, i32 162, i32 167, i32 178, i32 131, i32 218,
	i32 37, i32 22, i32 112, i32 90, i32 50, i32 60, i32 122, i32 83,
	i32 127, i32 163, i32 166, i32 4, i32 170, i32 2, i32 190, i32 214,
	i32 116, i32 19, i32 193, i32 89, i32 65, i32 30, i32 188, i32 225,
	i32 59, i32 111, i32 32, i32 128, i32 159, i32 140, i32 153, i32 17,
	i32 75, i32 74, i32 15, i32 169, i32 85, i32 223, i32 205, i32 177,
	i32 124, i32 177, i32 34, i32 212, i32 118, i32 139, i32 178, i32 122,
	i32 106, i32 202, i32 218, i32 178, i32 228, i32 54, i32 47, i32 28,
	i32 145, i32 193, i32 147, i32 215, i32 200, i32 35, i32 173, i32 75,
	i32 161, i32 1, i32 159, i32 12, i32 155, i32 151, i32 76, i32 221,
	i32 103, i32 112, i32 215, i32 177, i32 65, i32 66, i32 45, i32 208,
	i32 109, i32 194, i32 7, i32 55, i32 64, i32 224, i32 20, i32 109,
	i32 101, i32 62, i32 142, i32 216, i32 7, i32 170, i32 50, i32 202,
	i32 115, i32 141, i32 166, i32 221, i32 80, i32 113, i32 237, i32 17,
	i32 73, i32 89, i32 87, i32 120, i32 185, i32 135, i32 153, i32 106,
	i32 11, i32 90, i32 31, i32 136, i32 40, i32 238, i32 139, i32 199,
	i32 25, i32 73, i32 27, i32 194, i32 67, i32 88, i32 95, i32 113,
	i32 31, i32 104, i32 210, i32 37, i32 72, i32 108, i32 123, i32 87,
	i32 192, i32 86, i32 93, i32 206, i32 188, i32 129, i32 175, i32 183,
	i32 163, i32 130, i32 193, i32 201, i32 187, i32 10, i32 49, i32 174,
	i32 91, i32 150, i32 62, i32 136, i32 236, i32 150, i32 61, i32 211,
	i32 192, i32 227, i32 117, i32 137, i32 84, i32 159, i32 0, i32 143,
	i32 82, i32 70, i32 136, i32 125, i32 54, i32 110, i32 130, i32 88,
	i32 23, i32 225, i32 74, i32 129, i32 31, i32 73, i32 203, i32 158,
	i32 23, i32 4, i32 170, i32 123, i32 114, i32 181, i32 231, i32 172,
	i32 32, i32 3, i32 164, i32 213, i32 216, i32 30, i32 19, i32 197,
	i32 93, i32 36, i32 5, i32 155, i32 203, i32 76, i32 63, i32 147,
	i32 176, i32 121, i32 134, i32 237, i32 100, i32 39, i32 68, i32 26,
	i32 75, i32 78, i32 24, i32 152, i32 38, i32 213, i32 133, i32 103,
	i32 57, i32 165, i32 91, i32 61, i32 232, i32 214, i32 132, i32 46,
	i32 133, i32 145, i32 180, i32 78, i32 201, i32 186, i32 197, i32 154,
	i32 83, i32 61, i32 96, i32 153, i32 118, i32 196, i32 6, i32 15,
	i32 74, i32 189, i32 146, i32 174, i32 52, i32 70, i32 23, i32 158,
	i32 126, i32 65, i32 112, i32 55, i32 53, i32 107, i32 135, i32 80,
	i32 185, i32 228, i32 129, i32 64, i32 152
], align 4

@marshal_methods_number_of_classes = dso_local local_unnamed_addr constant i32 0, align 4

@marshal_methods_class_cache = dso_local local_unnamed_addr global [0 x %struct.MarshalMethodsManagedClass] zeroinitializer, align 8

; Names of classes in which marshal methods reside
@mm_class_names = dso_local local_unnamed_addr constant [0 x ptr] zeroinitializer, align 8

@mm_method_names = dso_local local_unnamed_addr constant [1 x %struct.MarshalMethodName] [
	%struct.MarshalMethodName {
		i64 u0x0000000000000000, ; name: 
		ptr @.MarshalMethodName.0_name; char* name
	} ; 0
], align 8

; get_function_pointer (uint32_t mono_image_index, uint32_t class_index, uint32_t method_token, void*& target_ptr)
@get_function_pointer = internal dso_local unnamed_addr global ptr null, align 8

; Functions

; Function attributes: memory(write, argmem: none, inaccessiblemem: none) "min-legal-vector-width"="0" mustprogress "no-trapping-math"="true" nofree norecurse nosync nounwind "stack-protector-buffer-size"="8" uwtable willreturn
define void @xamarin_app_init(ptr nocapture noundef readnone %env, ptr noundef %fn) local_unnamed_addr #0
{
	%fnIsNull = icmp eq ptr %fn, null
	br i1 %fnIsNull, label %1, label %2

1: ; preds = %0
	%putsResult = call noundef i32 @puts(ptr @.str.0)
	call void @abort()
	unreachable 

2: ; preds = %1, %0
	store ptr %fn, ptr @get_function_pointer, align 8, !tbaa !3
	ret void
}

; Strings
@.str.0 = private unnamed_addr constant [40 x i8] c"get_function_pointer MUST be specified\0A\00", align 1

;MarshalMethodName
@.MarshalMethodName.0_name = private unnamed_addr constant [1 x i8] c"\00", align 1

; External functions

; Function attributes: "no-trapping-math"="true" noreturn nounwind "stack-protector-buffer-size"="8"
declare void @abort() local_unnamed_addr #2

; Function attributes: nofree nounwind
declare noundef i32 @puts(ptr noundef) local_unnamed_addr #1
attributes #0 = { memory(write, argmem: none, inaccessiblemem: none) "min-legal-vector-width"="0" mustprogress "no-trapping-math"="true" nofree norecurse nosync nounwind "stack-protector-buffer-size"="8" "target-cpu"="generic" "target-features"="+fix-cortex-a53-835769,+neon,+outline-atomics,+v8a" uwtable willreturn }
attributes #1 = { nofree nounwind }
attributes #2 = { "no-trapping-math"="true" noreturn nounwind "stack-protector-buffer-size"="8" "target-cpu"="generic" "target-features"="+fix-cortex-a53-835769,+neon,+outline-atomics,+v8a" }

; Metadata
!llvm.module.flags = !{!0, !1, !7, !8, !9, !10}
!0 = !{i32 1, !"wchar_size", i32 4}
!1 = !{i32 7, !"PIC Level", i32 2}
!llvm.ident = !{!2}
!2 = !{!".NET for Android remotes/origin/release/9.0.1xx @ e7876a4f92d894b40c191a24c2b74f06d4bf4573"}
!3 = !{!4, !4, i64 0}
!4 = !{!"any pointer", !5, i64 0}
!5 = !{!"omnipotent char", !6, i64 0}
!6 = !{!"Simple C++ TBAA"}
!7 = !{i32 1, !"branch-target-enforcement", i32 0}
!8 = !{i32 1, !"sign-return-address", i32 0}
!9 = !{i32 1, !"sign-return-address-all", i32 0}
!10 = !{i32 1, !"sign-return-address-with-bkey", i32 0}
