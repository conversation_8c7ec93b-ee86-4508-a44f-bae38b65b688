using Microsoft.Extensions.Logging;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Client.Main.Helpers
{
    public static class RenderDebugHelper
    {
        private static ILogger _logger;
        private static readonly Dictionary<string, DateTime> _lastLogTimes = new();
        private static readonly TimeSpan _logCooldown = TimeSpan.FromSeconds(5);

        public static void Initialize(ILogger logger)
        {
            _logger = logger;
        }

        public static void LogRenderIssue(string category, string message, params object[] args)
        {
            if (_logger == null) return;

            var key = $"{category}:{message}";
            var now = DateTime.Now;

            if (_lastLogTimes.TryGetValue(key, out var lastTime) && 
                now - lastTime < _logCooldown)
            {
                return; // Skip logging to avoid spam
            }

            _lastLogTimes[key] = now;
            _logger.LogWarning($"[RENDER] {category}: {message}", args);
        }

        public static void LogCullingStats(int totalObjects, int culledObjects, int renderedObjects)
        {
            if (_logger == null) return;

            LogRenderIssue("CULLING", 
                "Total: {Total}, Culled: {Culled}, Rendered: {Rendered}, Cull Rate: {Rate:P1}",
                totalObjects, culledObjects, renderedObjects, 
                totalObjects > 0 ? (float)culledObjects / totalObjects : 0f);
        }

        public static void LogViewportInfo(Viewport viewport, Camera camera)
        {
            if (_logger == null) return;

            LogRenderIssue("VIEWPORT", 
                "Size: {Width}x{Height}, AspectRatio: {AspectRatio:F3}, FOV: {FOV:F1}, Near: {Near:F1}, Far: {Far:F1}",
                viewport.Width, viewport.Height, camera.AspectRatio, camera.FOV, camera.ViewNear, camera.ViewFar);
        }

        public static void LogFrustumInfo(BoundingFrustum frustum, Vector3 cameraPos)
        {
            if (_logger == null || frustum == null) return;

            var corners = frustum.GetCorners();
            var nearCorners = corners.Take(4).ToArray();
            var farCorners = corners.Skip(4).ToArray();

            var nearCenter = (nearCorners[0] + nearCorners[1] + nearCorners[2] + nearCorners[3]) / 4f;
            var farCenter = (farCorners[0] + farCorners[1] + farCorners[2] + farCorners[3]) / 4f;

            LogRenderIssue("FRUSTUM", 
                "Camera: {CameraPos}, Near: {NearCenter}, Far: {FarCenter}, Distance: {Distance:F1}",
                cameraPos, nearCenter, farCenter, Vector3.Distance(nearCenter, farCenter));
        }

        public static void LogObjectCulling(string objectName, Vector3 objectPos, Vector3 cameraPos, 
            bool distanceCulled, bool frustumCulled, float distance)
        {
            if (_logger == null) return;

            LogRenderIssue("OBJECT_CULLING", 
                "{Object} at {Pos} - Distance: {Distance:F1}, DistCulled: {DistCulled}, FrustumCulled: {FrustumCulled}",
                objectName, objectPos, distance, distanceCulled, frustumCulled);
        }

        public static void LogRenderState(GraphicsDevice device, string context)
        {
            if (_logger == null) return;

            LogRenderIssue("RENDER_STATE", 
                "{Context} - Cull: {Cull}, Depth: {Depth}, Blend: {Blend}",
                context, 
                device.RasterizerState?.CullMode ?? CullMode.None,
                device.DepthStencilState?.DepthBufferEnable ?? false,
                device.BlendState?.Name ?? "Unknown");
        }

        public static void LogProjectionMatrix(Matrix projection, string context)
        {
            if (_logger == null) return;

            // Extract FOV and aspect ratio from projection matrix
            float fov = 2f * (float)Math.Atan(1f / projection.M22) * 180f / (float)Math.PI;
            float aspectRatio = projection.M22 / projection.M11;
            float nearPlane = projection.M43 / projection.M33;
            float farPlane = projection.M43 / (projection.M33 - 1f);

            LogRenderIssue("PROJECTION", 
                "{Context} - FOV: {FOV:F1}°, Aspect: {Aspect:F3}, Near: {Near:F1}, Far: {Far:F1}",
                context, fov, aspectRatio, nearPlane, farPlane);
        }

        public static void CheckForRenderingIssues(GraphicsDevice device, Camera camera)
        {
            if (_logger == null) return;

            // Check for common rendering issues
            var viewport = device.Viewport;
            
            if (viewport.Width <= 0 || viewport.Height <= 0)
            {
                LogRenderIssue("ERROR", "Invalid viewport size: {Width}x{Height}", viewport.Width, viewport.Height);
            }

            if (camera.AspectRatio <= 0)
            {
                LogRenderIssue("ERROR", "Invalid camera aspect ratio: {AspectRatio}", camera.AspectRatio);
            }

            if (camera.ViewNear >= camera.ViewFar)
            {
                LogRenderIssue("ERROR", "Invalid camera near/far planes: Near={Near}, Far={Far}", 
                    camera.ViewNear, camera.ViewFar);
            }

            if (camera.FOV <= 0 || camera.FOV >= 180)
            {
                LogRenderIssue("ERROR", "Invalid camera FOV: {FOV}", camera.FOV);
            }

            // Check if aspect ratio matches viewport
            float viewportAspect = (float)viewport.Width / viewport.Height;
            if (Math.Abs(camera.AspectRatio - viewportAspect) > 0.1f)
            {
                LogRenderIssue("WARNING", "Camera aspect ratio mismatch: Camera={CameraAspect:F3}, Viewport={ViewportAspect:F3}",
                    camera.AspectRatio, viewportAspect);
            }
        }

        public static void ClearOldLogs()
        {
            var cutoff = DateTime.Now - TimeSpan.FromMinutes(5);
            var keysToRemove = _lastLogTimes.Where(kvp => kvp.Value < cutoff).Select(kvp => kvp.Key).ToList();
            
            foreach (var key in keysToRemove)
            {
                _lastLogTimes.Remove(key);
            }
        }
    }
}
