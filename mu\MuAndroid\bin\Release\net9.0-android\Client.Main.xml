<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Client.Main</name>
    </assembly>
    <members>
        <member name="F:Client.Main.Constants.SHOW_NAMES_ON_HOVER">
            <summary>
            Enables drawing of object names when hovered with the mouse.
            </summary>
        </member>
        <member name="M:Client.Main.Content.BMDLoader.GetModelBuffers(Client.Data.BMD.BMD,System.Int32,Microsoft.Xna.Framework.Color,Microsoft.Xna.Framework.Matrix[],Microsoft.Xna.Framework.Graphics.DynamicVertexBuffer@,Microsoft.Xna.Framework.Graphics.DynamicIndexBuffer@)">
            <summary>
            Builds (or updates) the dynamic vertex/index buffers for the given mesh.
            Uses ArrayPool to eliminate per‑frame allocations.
            </summary>
        </member>
        <member name="M:Client.Main.Controllers.SoundController.PreloadSound(System.String)">
            <summary>
            Preloads a sound effect into the cache to avoid loading delays during playback.
            This is a new, more generic method name.
            </summary>
        </member>
        <member name="M:Client.Main.Controllers.SoundController.PlayBufferWithAttenuation(System.String,Microsoft.Xna.Framework.Vector3,Microsoft.Xna.Framework.Vector3,System.Single,System.Boolean)">
            <summary>
            Plays a sound effect with volume attenuation based on distance.
            Can be looped if 'loop' parameter is true.
            </summary>
        </member>
        <member name="T:Client.Main.Controls.DynamicLight">
            <summary>
            Simple dynamic light source used for terrain and object lighting.
            </summary>
        </member>
        <member name="P:Client.Main.Controls.DynamicLight.Owner">
            <summary>The WorldObject that owns this light source.</summary>
        </member>
        <member name="P:Client.Main.Controls.DynamicLight.Position">
            <summary>World position of the light.</summary>
        </member>
        <member name="P:Client.Main.Controls.DynamicLight.Color">
            <summary>RGB color of the light in the 0..1 range.</summary>
        </member>
        <member name="P:Client.Main.Controls.DynamicLight.Radius">
            <summary>Effective radius of the light.</summary>
        </member>
        <member name="P:Client.Main.Controls.DynamicLight.Intensity">
            <summary>Light intensity multiplier.</summary>
        </member>
        <member name="M:Client.Main.Controls.TerrainControl.FlushGrassBatch">
            <summary>Renders the buffered grass tufts and restores GPU states.</summary>
        </member>
        <member name="T:Client.Main.Controls.UI.FloatingText">
            <summary>
            Represents a single on-screen floating text notification.
            </summary>
        </member>
        <member name="P:Client.Main.Controls.UI.FloatingText.CreationTime">
            <summary>
            Timestamp when this instance was created (seconds since game start).
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.FloatingText.SetCenterY(System.Single)">
            <summary>
            Updates the vertical center for layout in NotificationManager.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.FloatingText.MoveUp(System.Single)">
            <summary>
            Moves the text up or down by the given delta.
            </summary>
        </member>
        <member name="T:Client.Main.Controls.UI.Game.DynamicLayoutControl">
            <summary>
            Base class for dynamic control layout.
            Responsible for loading data from JSON files, scaling, and setting custom alpha values.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.Game.Inventory.InventoryControl.Preload">
            <summary>
            Preloads inventory data and item textures without showing the window.
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Client.Main.Controls.UI.Game.Inventory.InventoryControl.GetSlotHighlightColor(Microsoft.Xna.Framework.Point,Client.Main.Controls.UI.Game.Inventory.InventoryItem)" -->
        <member name="M:Client.Main.Controls.UI.Game.Inventory.InventoryControl.IsSlotInDropArea(Microsoft.Xna.Framework.Point,Microsoft.Xna.Framework.Point,Client.Main.Controls.UI.Game.Inventory.InventoryItem)">
            <summary>
            Checks if the given slot belongs to the area that would be occupied by an item dropped at hoveredSlot
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.Game.Inventory.InventoryControl.IsSlotOccupied(Microsoft.Xna.Framework.Point)">
            <summary>
            Checks if the given slot is occupied by any item
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.Game.MapListControl.AddButtons">
            <summary>
            Adds map buttons to the control.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.Game.MapListControl.ArrangeMapButtons">
            <summary>
            Arranges the map buttons in a vertical column with the specified inner padding.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.ChatLogWindow.AddMessage(System.String,System.String,Client.Main.Models.MessageType)">
            <summary>
            Adds a message to the appropriate lists. Future network data will come here.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.ChatLogWindow.ChangeViewType(Client.Main.Models.MessageType)">
            <summary>
            Changes the currently displayed message type.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.ChatLogWindow.CycleSize">
            <summary>
            Cycles the number of displayed lines (like C++ F4/button).
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.ChatLogWindow.SetShowingLines(System.Int32)">
            <summary>
            Sets a specific number of displayed lines.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.ChatLogWindow.ShowFrame(System.Boolean)">
            <summary>
            Shows or hides the frame (scrollbar, handle).
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.ChatLogWindow.ToggleFrame">
            <summary>
            Toggles frame visibility.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.ChatLogWindow.CycleBackgroundAlpha">
            <summary>
            Cycles background transparency (like C++ button).
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.ChatLogWindow.SetBackgroundAlpha(System.Single)">
            <summary>
            Sets a specific background transparency.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.ChatLogWindow.ScrollLines(System.Int32)">
            <summary>
            Scrolls the view by a specified number of lines (e.g., for PageUp/Down).
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.ChatLogWindow.Clear(Client.Main.Models.MessageType)">
            <summary>
            Clears messages of a given type.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.ChatLogWindow.ClearAll">
            <summary>
            Clears all messages from all lists.
            </summary>
        </member>
        <member name="P:Client.Main.Controls.UI.LabelControl.Text">
            <summary>
            Text to display. Changing this recalculates the rendered text.
            </summary>
        </member>
        <member name="P:Client.Main.Controls.UI.LabelControl.TextArgs">
            <summary>
            Format arguments.
            </summary>
        </member>
        <member name="P:Client.Main.Controls.UI.LabelControl.FontSize">
            <summary>
            Desired font size on screen.
            </summary>
        </member>
        <member name="P:Client.Main.Controls.UI.Login.LoginDialog.Username">
            <summary>
            Gets the username entered in the text field.
            </summary>
        </member>
        <member name="P:Client.Main.Controls.UI.Login.LoginDialog.Password">
            <summary>
            Gets the password entered in the text field.
            </summary>
        </member>
        <member name="E:Client.Main.Controls.UI.Login.LoginDialog.LoginAttempt">
            <summary>
            Invoked when the user confirms login (clicks OK or presses Enter in the password field).
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.Login.LoginDialog.FocusUsername">
            <summary>
            Sets focus on the username field (called from the scene).
            </summary>
        </member>
        <member name="T:Client.Main.Controls.UI.NotificationManager">
            <summary>
            Manages on-screen floating text notifications.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.NotificationManager.AddNotification(System.String,Microsoft.Xna.Framework.Color)">
            <summary>
            Adds a new notification, using the last known game time.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.NotificationManager.AddNotification(System.String,Microsoft.Xna.Framework.Color,Microsoft.Xna.Framework.GameTime)">
            <summary>
            Adds a new notification at the specified game time.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.UI.NotificationManager.RecalculateStack">
            <summary>
            Arranges notifications in a vertical stack without overlap.
            </summary>
        </member>
        <member name="T:Client.Main.Controls.WalkableWorldControl">
            <summary>
            Extends WorldControl to support click‐to‐move gameplay.
            </summary>
        </member>
        <member name="P:Client.Main.Controls.WalkableWorldControl.Walker">
            <summary>
            The player's walker object.
            </summary>
        </member>
        <member name="P:Client.Main.Controls.WalkableWorldControl.MouseTileX">
            <summary>
            The X coordinate of the tile currently under the mouse.
            </summary>
        </member>
        <member name="P:Client.Main.Controls.WalkableWorldControl.MouseTileY">
            <summary>
            The Y coordinate of the tile currently under the mouse.
            </summary>
        </member>
        <member name="P:Client.Main.Controls.WalkableWorldControl.ExtraHeight">
            <summary>
            Height offset applied when placing the cursor above terrain.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.WalkableWorldControl.#ctor(System.Int16)">
            <summary>
            Initializes a walkable world with default walker.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.WalkableWorldControl.#ctor(System.Int16,Client.Main.Objects.WalkerObject)">
            <summary>
            Initializes a walkable world with a specified walker.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.WalkableWorldControl.CalculateMouseTilePos">
            <summary>
            Calculates which terrain tile is under the input cursor (mouse or touch) by raycasting.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.WalkableWorldControl.FindMonsterAtTile(System.Byte,System.Byte)">
            <summary>
            Returns the first <see cref="T:Client.Main.Objects.Monsters.MonsterObject"/> occupying the given tile, or <c>null</c>.
            </summary>
        </member>
        <member name="T:Client.Main.Controls.WorldControl">
            <summary>
            Base class for rendering and managing world objects in a game scene.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.WorldControl.TryGetWalkerById(System.UInt16,Client.Main.Objects.WalkerObject@)">
            <summary>
            Attempts to retrieve a walker by its network ID.
            Checks local player first, then dictionary, then full Objects search as fallback.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.WorldControl.RemoveObject(Client.Main.Objects.WorldObject)">
            <summary>
            Removes an object from the scene and dictionary if applicable.
            </summary>
        </member>
        <member name="M:Client.Main.Controls.WorldControl.IsLightInView(Client.Main.Controls.DynamicLight)">
            <summary>
            Efficiently checks if a dynamic light's area of effect intersects with the camera's view frustum.
            </summary>
            <param name="light">The dynamic light to check.</param>
            <returns>True if the light's sphere is at least partially in view, otherwise false.</returns>
        </member>
        <member name="T:Client.Main.Core.Client.SkillEntryState">
            <summary>
            Represents the state of a learned skill, including its ID, level, and display values.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Client.SkillEntryState.SkillId">
            <summary>
            Gets or sets the unique identifier of the skill.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Client.SkillEntryState.SkillLevel">
            <summary>
            Gets or sets the current level of the skill.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Client.SkillEntryState.DisplayValue">
            <summary>
            Gets or sets the current display value of the skill, if applicable.
            This could represent a percentage or a numerical value shown to the player.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Client.SkillEntryState.NextDisplayValue">
            <summary>
            Gets or sets the next display value of the skill, often shown in tooltips to indicate the value after leveling up.
            </summary>
        </member>
        <member name="T:Client.Main.Core.Client.CharacterState">
            <summary>
            Holds the state of the currently logged-in character, including basic info, stats, inventory, and skills.
            This class is responsible for tracking and updating the character's attributes as received from the server.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.#ctor(Microsoft.Extensions.Logging.ILoggerFactory)">
            <summary>
            Initializes a new instance of the <see cref="T:Client.Main.Core.Client.CharacterState"/> class.
            </summary>
            <param name="loggerFactory">The logger factory for creating a logger.</param>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.StashPickedItem(System.Byte[])">
            <summary>
            Stores item data temporarily before a pickup attempt.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.CommitStashedItem(System.Byte)">
            <summary>
            Commits the stashed item to the specified inventory slot.
            Clears the stash on success.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.ClearPendingPickedItem">
            <summary>
            Clears any stashed item data. Call this if a pickup attempt definitively fails.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.UpdateCoreCharacterInfo(System.UInt16,System.String,MUnique.OpenMU.Network.Packets.CharacterClassNumber,System.UInt16,System.Byte,System.Byte,System.UInt16)">
            <summary>
            Updates core character identification data. More detailed stats (HP, MP, attributes etc.)
            are expected to be updated by specific server packets like CharacterInformation or individual stat updates.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.UpdatePosition(System.Byte,System.Byte)">
            <summary>
            Updates the character's position coordinates.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.UpdateDirection(System.Byte)">
            <summary>
            Updates the character's direction.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.UpdateMap(System.UInt16)">
            <summary>
            Updates the character's current map ID.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.UpdateLevelAndExperience(System.UInt16,System.UInt64,System.UInt64,System.UInt16)">
            <summary>
            Updates the character's level, experience, and level up points.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.UpdateMasterLevelAndExperience(System.UInt16,System.UInt64,System.UInt64,System.UInt16)">
            <summary>
            Updates the character's master level, master experience, and master level up points.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.AddExperience(System.UInt32)">
            <summary>
            Adds experience points to the character's current experience.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.UpdateStats(System.UInt16,System.UInt16,System.UInt16,System.UInt16,System.UInt16)">
            <summary>
            Updates the character's base stats (Strength, Agility, Vitality, Energy, Leadership).
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.IncrementStat(MUnique.OpenMU.Network.Packets.CharacterStatAttribute,System.UInt16)">
            <summary>
            Increments a specific character stat attribute by a given amount.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.UpdateInventoryZen(System.UInt32)">
            <summary>
            Updates the amount of Zen in the character's inventory.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.UpdateStatus(MUnique.OpenMU.Network.Packets.ServerToClient.CharacterStatus,MUnique.OpenMU.Network.Packets.CharacterHeroState)">
            <summary>
            Updates the character's status and hero state.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.ClearInventory">
            <summary>
            Clears all items from the character's inventory.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.AddOrUpdateInventoryItem(System.Byte,System.Byte[])">
            <summary>
            Adds or updates an item in the character's inventory at a specific slot.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.RemoveInventoryItem(System.Byte)">
            <summary>
            Removes an item from the character's inventory at a specific slot.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.UpdateItemDurability(System.Byte,System.Byte)">
            <summary>
            Updates the durability of an item in the inventory at a specific slot.
            Assumes durability is at index 2 in the item data byte array.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.ClearSkillList">
            <summary>
            Clears the character's skill list.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.AddOrUpdateSkill(Client.Main.Core.Client.SkillEntryState)">
            <summary>
            Adds or updates a skill in the character's skill list.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.RemoveSkill(System.UInt16)">
            <summary>
            Removes a skill from the character's skill list by its skill ID.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.GetSkills">
            <summary>
            Gets all skills in the skill list as an enumerable collection, ordered by skill ID.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.GetInventoryDisplay">
            <summary>
            Gets a formatted string representation of the character's inventory.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.GetFormattedStatsList">
            <summary>
            Gets a list of key-value pairs representing character stats for UI display.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.GetStatsDisplay">
            <summary>
            Gets a formatted string representation of the character's stats.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.GetInventoryItems">
            <summary>
            Gets a read-only dictionary representation of the current inventory items.
            Key is the slot number, Value is the raw item data.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.FormatInventoryItem(System.Byte,System.Byte[])">
            <summary>
            Formats item data from a specific slot into a display string.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.GetSkillListDisplay">
            <summary>
            Gets a formatted string representation of the character's skill list.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.ParseItemDetails(System.Byte[])">
            <summary>
            Parses item data bytes based on common item structure (Season 6 assumed).
            Extracts item level, skill, luck, option, excellent options, ancient options, level 380 option, harmony option, socket bonus, and socket options.
            </summary>
            <param name="itemData">Byte array of item data (typically 8, 12, or more bytes).</param>
            <returns>String containing parsed item details, or an empty string if no details are parsed.</returns>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.ParseExcellentOptions(System.Byte)">
            <summary>
            Parses the excellent options byte to extract enabled excellent options flags.
            </summary>
            <param name="excByte">The excellent options byte (typically itemData[3] in S6).</param>
            <returns>A list of strings representing the enabled excellent options.</returns>
        </member>
        <member name="M:Client.Main.Core.Client.CharacterState.ParseSocketOption(System.Byte)">
            <summary>
            Parses a socket option byte into a readable string (placeholder).
            Requires a lookup table mapping byte values to seed sphere types.
            </summary>
            <param name="socketByte">The socket option byte.</param>
            <returns>A string representing the socket option (placeholder).</returns>
        </member>
        <member name="T:Client.Main.Core.Client.TargetProtocolVersion">
            <summary>
            Enumeration representing different protocol versions for client compatibility.
            </summary>
        </member>
        <member name="T:Client.Main.Core.Client.ClientConnectionState">
            <summary>
            Enumeration defining the connection states of the client.
            </summary>
        </member>
        <member name="T:Client.Main.Core.Client.ScopeManager">
            <summary>
            Manages objects that are currently within the player's scope of view or interaction range.
            This class is responsible for tracking players, NPCs, items, and money that are visible to the client.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.#ctor(Microsoft.Extensions.Logging.ILoggerFactory,Client.Main.Core.Client.CharacterState)">
            <summary>
            Initializes a new instance of the <see cref="T:Client.Main.Core.Client.ScopeManager"/> class.
            </summary>
            <param name="loggerFactory">The logger factory used to create a logger for this scope manager.</param>
            <param name="characterState">The character state which provides information about the player's current status and position.</param>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.AddOrUpdatePlayerInScope(System.UInt16,System.UInt16,System.Byte,System.Byte,System.String)">
            <summary>
            Adds or updates a player object in the scope.
            If a player with the same masked ID already exists, its information is updated; otherwise, a new player is added.
            </summary>
            <param name="maskedId">The masked ID of the player.</param>
            <param name="rawId">The raw ID of the player.</param>
            <param name="x">The X-coordinate of the player's position.</param>
            <param name="y">The Y-coordinate of the player's position.</param>
            <param name="name">The name of the player.</param>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.AddOrUpdateNpcInScope(System.UInt16,System.UInt16,System.Byte,System.Byte,System.UInt16,System.String)">
            <summary>
            Adds or updates an NPC object in the scope.
            If an NPC with the same masked ID already exists, its information is updated; otherwise, a new NPC is added.
            </summary>
            <param name="maskedId">The masked ID of the NPC.</param>
            <param name="rawId">The raw ID of the NPC.</param>
            <param name="x">The X-coordinate of the NPC's position.</param>
            <param name="y">The Y-coordinate of the NPC's position.</param>
            <param name="typeNumber">The type number of the NPC.</param>
            <param name="name">The optional name of the NPC.</param>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.AddOrUpdateItemInScope(System.UInt16,System.UInt16,System.Byte,System.Byte,System.ReadOnlySpan{System.Byte})">
            <summary>
            Adds or updates an item object in the scope.
            If an item with the same masked ID already exists, its information is updated; otherwise, a new item is added.
            </summary>
            <param name="maskedId">The masked ID of the item.</param>
            <param name="rawId">The raw ID of the item.</param>
            <param name="x">The X-coordinate of the item's position.</param>
            <param name="y">The Y-coordinate of the item's position.</param>
            <param name="itemData">The raw data of the item.</param>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.AddOrUpdateMoneyInScope(System.UInt16,System.UInt16,System.Byte,System.Byte,System.UInt32)">
            <summary>
            Adds or updates money object in the scope.
            If money with the same masked ID already exists, its information is updated; otherwise, new money is added.
            </summary>
            <param name="maskedId">The masked ID of the money.</param>
            <param name="rawId">The raw ID of the money.</param>
            <param name="x">The X-coordinate of the money's position.</param>
            <param name="y">The Y-coordinate of the money's position.</param>
            <param name="amount">The amount of money.</param>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.RemoveObjectFromScope(System.UInt16)">
            <summary>
            Removes an object from the scope based on its masked ID.
            </summary>
            <param name="maskedId">The masked ID of the object to remove.</param>
            <returns><c>true</c> if the object was successfully removed; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.TryUpdateScopeObjectPosition(System.UInt16,System.Byte,System.Byte)">
            <summary>
            Tries to update the position of an object in the scope.
            </summary>
            <param name="maskedId">The masked ID of the object to update.</param>
            <param name="x">The new X-coordinate.</param>
            <param name="y">The new Y-coordinate.</param>
            <returns><c>true</c> if the object's position was updated; otherwise, <c>false</c> if the object was not found in scope.</returns>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.ScopeContains(System.UInt16)">
            <summary>
            Checks if the scope contains an object with the specified masked ID.
            </summary>
            <param name="maskedId">The masked ID to check for.</param>
            <returns><c>true</c> if an object with the given ID is in the scope; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.GetScopeItems(Client.Main.Core.Models.ScopeObjectType)">
            <summary>
            Gets all objects in the scope of a specific type.
            </summary>
            <param name="type">The <see cref="T:Client.Main.Core.Models.ScopeObjectType"/> to filter by.</param>
            <returns>An enumerable collection of <see cref="T:Client.Main.Core.Models.ScopeObject"/> of the specified type.</returns>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.ClearScope(System.Boolean)">
            <summary>
            Clears the scope, removing all objects. Optionally keeps the player's own character in scope.
            </summary>
            <param name="clearSelf">If set to <c>true</c>, clears even the player's own character from scope. Default is <c>false</c>.</param>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.GetScopeListDisplay">
            <summary>
            Gets a formatted string representation of all objects currently in scope.
            </summary>
            <returns>A string that lists all objects in scope, using their <see cref="M:Client.Main.Core.Models.ScopeObject.ToString"/> representation.</returns>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.TryGetScopeObjectName(System.UInt16,System.String@)">
            <summary>
            Tries to get the name of a scope object based on its raw ID.
            </summary>
            <param name="rawId">The raw ID of the object.</param>
            <param name="name">When this method returns, contains the name of the object, or <c>null</c> if the object is not found or has no name.</param>
            <returns><c>true</c> if the object's name was successfully retrieved; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.FindNearestPickupItemRawId">
            <summary>
            Finds the raw ID of the nearest pickupable item (Item or Money) in scope.
            </summary>
            <returns>The raw ID of the nearest pickupable item, or <c>null</c> if no items or money are in pickup range.</returns>
        </member>
        <member name="M:Client.Main.Core.Client.ScopeManager.DistanceSquared(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Calculates the squared distance between two points.
            </summary>
            <param name="x1">The X-coordinate of the first point.</param>
            <param name="y1">The Y-coordinate of the first point.</param>
            <param name="x2">The X-coordinate of the second point.</param>
            <param name="y2">The Y-coordinate of the second point.</param>
            <returns>The squared distance between the two points.</returns>
        </member>
        <member name="T:Client.Main.Core.Models.ScopeObjectType">
            <summary>
            Enumeration defining the types of objects that can be in the scope of the player.
            </summary>
        </member>
        <member name="T:Client.Main.Core.Models.ScopeObject">
            <summary>
            Abstract base class for all objects that can be in the player's scope.
            Provides common properties and methods for all scope objects.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ScopeObject.Id">
            <summary>
            Gets the masked identifier of the scope object. This ID is used as a key in the scope management.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ScopeObject.RawId">
            <summary>
            Gets the original raw identifier of the scope object as received from the server.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ScopeObject.PositionX">
            <summary>
            Gets or sets the X-coordinate of the object's position on the map.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ScopeObject.PositionY">
            <summary>
            Gets or sets the Y-coordinate of the object's position on the map.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ScopeObject.ObjectType">
            <summary>
            Gets the type of the scope object. Must be implemented by derived classes.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ScopeObject.LastUpdate">
            <summary>
            Gets or sets the timestamp of the last update received for this scope object.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Models.ScopeObject.#ctor(System.UInt16,System.UInt16,System.Byte,System.Byte)">
            <summary>
            Initializes a new instance of the <see cref="T:Client.Main.Core.Models.ScopeObject"/> class.
            </summary>
            <param name="maskedId">The masked identifier of the scope object.</param>
            <param name="rawId">The raw identifier of the scope object.</param>
            <param name="x">The X-coordinate of the object's position.</param>
            <param name="y">The Y-coordinate of the object's position.</param>
        </member>
        <member name="M:Client.Main.Core.Models.ScopeObject.ToString">
            <inheritdoc />
        </member>
        <member name="T:Client.Main.Core.Models.PlayerScopeObject">
            <summary>
            Represents a player character object within the scope.
            Inherits from <see cref="T:Client.Main.Core.Models.ScopeObject"/> and adds player-specific properties.
            </summary>
        </member>
        <member name="T:Client.Main.Core.Models.NpcScopeObject">
            <summary>
            Represents a Non-Player Character (NPC) or Monster object within the scope.
            Inherits from <see cref="T:Client.Main.Core.Models.ScopeObject"/> and adds NPC-specific properties.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.NpcScopeObject.Name">
            <summary>
            Gets or sets the name of the NPC, can be null or empty if the NPC has no specific name.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.NpcScopeObject.TypeNumber">
            <summary>
            Gets or sets the type number of the NPC, identifying its specific kind.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.NpcScopeObject.ObjectType">
            <inheritdoc />
        </member>
        <member name="M:Client.Main.Core.Models.NpcScopeObject.#ctor(System.UInt16,System.UInt16,System.Byte,System.Byte,System.UInt16,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Client.Main.Core.Models.NpcScopeObject"/> class.
            </summary>
            <param name="maskedId">The masked identifier of the NPC scope object.</param>
            <param name="rawId">The raw identifier of the NPC scope object.</param>
            <param name="x">The X-coordinate of the NPC's position.</param>
            <param name="y">The Y-coordinate of the NPC's position.</param>
            <param name="typeNumber">The type number of the NPC.</param>
            <param name="name">The optional name of the NPC.</param>
        </member>
        <member name="M:Client.Main.Core.Models.NpcScopeObject.ToString">
            <inheritdoc />
        </member>
        <member name="T:Client.Main.Core.Models.ItemScopeObject">
            <summary>
            Represents an Item object dropped on the ground within the scope.
            Inherits from <see cref="T:Client.Main.Core.Models.ScopeObject"/> and adds item-specific properties.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ItemScopeObject.ItemDescription">
            <summary>
            Gets the description of the item, usually its name and basic attributes.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ItemScopeObject.ItemData">
            <summary>
            Gets the original item data as received from the server.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ItemScopeObject.ObjectType">
            <inheritdoc />
        </member>
        <member name="M:Client.Main.Core.Models.ItemScopeObject.#ctor(System.UInt16,System.UInt16,System.Byte,System.Byte,System.ReadOnlySpan{System.Byte})">
            <summary>
            Initializes a new instance of the <see cref="T:Client.Main.Core.Models.ItemScopeObject"/> class.
            </summary>
            <param name="maskedId">The masked identifier of the item scope object.</param>
            <param name="rawId">The raw identifier of the item scope object.</param>
            <param name="x">The X-coordinate of the item's position.</param>
            <param name="y">The Y-coordinate of the item's position.</param>
            <param name="itemData">The raw data of the item.</param>
        </member>
        <member name="M:Client.Main.Core.Models.ItemScopeObject.ToString">
            <inheritdoc />
        </member>
        <member name="T:Client.Main.Core.Models.MoneyScopeObject">
            <summary>
            Represents a Money (Zen) object dropped on the ground within the scope.
            Inherits from <see cref="T:Client.Main.Core.Models.ScopeObject"/> and adds money-specific properties.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.MoneyScopeObject.Amount">
            <summary>
            Gets or sets the amount of money (Zen) this object represents.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.MoneyScopeObject.ObjectType">
            <inheritdoc />
        </member>
        <member name="M:Client.Main.Core.Models.MoneyScopeObject.#ctor(System.UInt16,System.UInt16,System.Byte,System.Byte,System.UInt32)">
            <summary>
            Initializes a new instance of the <see cref="T:Client.Main.Core.Models.MoneyScopeObject"/> class.
            </summary>
            <param name="maskedId">The masked identifier of the money scope object.</param>
            <param name="rawId">The raw identifier of the money scope object.</param>
            <param name="x">The X-coordinate of the money's position.</param>
            <param name="y">The Y-coordinate of the money's position.</param>
            <param name="amount">The amount of money.</param>
        </member>
        <member name="M:Client.Main.Core.Models.MoneyScopeObject.ToString">
            <inheritdoc />
        </member>
        <member name="T:Client.Main.Core.Models.ServerInfo">
            <summary>
            Represents information about a game server, as received from the Connect Server.
            This class holds details such as the server's ID, load percentage, and optionally its name, IP address, and port.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ServerInfo.ServerId">
            <summary>
            Gets or sets the unique identifier for the game server.
            This ID is used to reference the server in communication with the Connect Server.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ServerInfo.LoadPercentage">
            <summary>
            Gets or sets the current load percentage of the game server.
            This value indicates how busy the server is, typically ranging from 0% to 100%.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ServerInfo.ServerName">
            <summary>
            Gets or sets the optional name of the game server.
            The server name might not always be provided by the protocol, so it can be null.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ServerInfo.IpAddress">
            <summary>
            Gets or sets the optional IP address of the game server.
            This is typically populated after requesting connection information for a specific server.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Models.ServerInfo.Port">
            <summary>
            Gets or sets the port number of the game server.
            This is typically populated after requesting connection information for a specific server.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Models.ServerInfo.ToString">
            <inheritdoc />
        </member>
        <member name="T:Client.Main.Core.Utilities.CharacterClassDatabase">
            <summary>
            Provides mapping between character class numbers and names.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Utilities.CharacterClassDatabase.GetClassName(MUnique.OpenMU.Network.Packets.CharacterClassNumber)">
            <summary>
            Gets the name of the character class based on its number.
            </summary>
            <param name="classNumber">The character class number.</param>
            <returns>The name of the class, or the number as a string if not found.</returns>
        </member>
        <member name="M:Client.Main.Core.Utilities.CharacterClassDatabase.TryParseClassFromAppearance(System.ReadOnlySpan{System.Byte},MUnique.OpenMU.Network.Packets.CharacterClassNumber@)">
            <summary>
            Tries to parse the character class number from the appearance data.
            This depends heavily on the protocol version and appearance data structure.
            This is a common S6+ approach where class is in the upper bits of the first appearance byte.
            </summary>
            <param name="appearanceData">The appearance data span.</param>
            <param name="classNumber">The parsed class number.</param>
            <returns>True if parsing was potentially successful, false otherwise.</returns>
        </member>
        <member name="T:Client.Main.Core.Utilities.ItemDatabase">
            <summary>
            A static class containing the item name database based on their group and ID.
            Data is generated from OpenMU initializers (Season 6).
            </summary>
        </member>
        <member name="F:Client.Main.Core.Utilities.ItemDatabase.Items">
            <summary>
            Main dictionary: Outer key is Group (byte), Value is an inner dictionary.
            Inner dictionary: Key is Number/ID (short), Value is Name (string).
            </summary>
        </member>
        <member name="M:Client.Main.Core.Utilities.ItemDatabase.GetItemName(System.Byte,System.Int16)">
            <summary>
            Gets the item name based on its group and ID (number).
            </summary>
            <param name="group">The item group.</param>
            <param name="id">The item ID (number) within the group.</param>
            <returns>The item name, or null if not found.</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Client.Main.Core.Utilities.ItemDatabase.GetItemName(System.ReadOnlySpan{System.Byte})" -->
        <member name="T:Client.Main.Core.Utilities.MapDatabase">
            <summary>
            Provides mapping between map numbers and their names.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Utilities.MapDatabase.GetMapName(System.UInt16)">
            <summary>
            Gets the name of the map based on its number.
            </summary>
            <param name="mapId">The map number (ID).</param>
            <returns>The name of the map, or a default string if not found.</returns>
        </member>
        <member name="M:Client.Main.Core.Utilities.NpcDatabase.GetNpcName(System.UInt16)">
            <summary>Zwraca nazwę NPC/Monstera po jego TypeId.</summary>
        </member>
        <member name="M:Client.Main.Core.Utilities.NpcDatabase.TryGetNpcType(System.UInt16,System.Type@)">
            <summary>Próbuje zwrócić klasę (Type) skojarzoną z danym TypeId.</summary>
        </member>
        <member name="P:Client.Main.Core.Utilities.NpcDatabase.AllNpcTypes">
            <summary>Pełna mapa typów (do iteracji lub debugowania).</summary>
        </member>
        <member name="T:Client.Main.Core.Utilities.PacketHandlerAttribute">
            <summary>
             Attribute to mark methods as handlers for specific network packets, identified by their main and sub codes.
             This attribute is used to route incoming packets to the appropriate handling methods.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Utilities.PacketHandlerAttribute.MainCode">
            <summary>
            Gets the main operation code of the packet that this handler is designed to process.
            </summary>
        </member>
        <member name="P:Client.Main.Core.Utilities.PacketHandlerAttribute.SubCode">
            <summary>
            Gets the sub-operation code of the packet that this handler is designed to process.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Utilities.PacketHandlerAttribute.#ctor(System.Byte,System.Byte)">
            <summary>
            Initializes a new instance of the <see cref="T:Client.Main.Core.Utilities.PacketHandlerAttribute"/> class.
            </summary>
            <param name="mainCode">The main operation code of the packet.</param>
            <param name="subCode">The sub-operation code of the packet. Use 0xFF if the packet does not have a sub-code.</param>
        </member>
        <member name="F:Client.Main.Core.Utilities.PlayerActionMapper.ClientToServerMap">
            <summary> Odwrócone mapowanie + ręczne wpisy dla wszystkich animacji ataku. </summary>
        </member>
        <member name="T:Client.Main.Core.Utilities.SubCodeHolder">
            <summary>
             Static class holding a set of Game Server packet codes that are known to utilize sub-codes.
             This is crucial for the PacketRouter to correctly identify packet handlers, as packets with sub-codes require both main and sub-code for routing.
            </summary>
        </member>
        <member name="F:Client.Main.Core.Utilities.SubCodeHolder.CodesWithSubCode">
            <summary>
             HashSet containing the main codes of Game Server packets that have sub-codes.
             Initialized with codes from ServerToClientPackets.cs in MUnique.OpenMU.Network and additional codes identified to use sub-codes.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Utilities.SubCodeHolder.ContainsSubCode(System.Byte)">
            <summary>
             Checks if a given Game Server packet code is known to have a sub-code.
            </summary>
            <param name="code">The main packet code to check.</param>
            <returns><c>true</c> if the packet code is in the list of codes with sub-codes; otherwise, <c>false</c>.</returns>
        </member>
        <member name="T:Client.Main.Core.Utilities.ConnectServerSubCodeHolder">
            <summary>
             Static class holding a set of Connect Server packet codes that are known to utilize sub-codes.
             Similar to <see cref="T:Client.Main.Core.Utilities.SubCodeHolder"/> but specifically for Connect Server packets.
            </summary>
        </member>
        <member name="F:Client.Main.Core.Utilities.ConnectServerSubCodeHolder.CodesWithSubCode">
            <summary>
             HashSet containing the main codes of Connect Server packets that have sub-codes.
             Initialized with codes from ConnectServerPackets.cs in MUnique.OpenMU.Network.
            </summary>
        </member>
        <member name="M:Client.Main.Core.Utilities.ConnectServerSubCodeHolder.ContainsSubCode(System.Byte)">
            <summary>
             Checks if a given Connect Server packet code is known to have a sub-code.
            </summary>
            <param name="code">The main packet code to check.</param>
            <returns><c>true</c> if the packet code is in the list of Connect Server codes with sub-codes; otherwise, <c>false</c>.</returns>
        </member>
        <member name="T:Client.Main.Input.InputManager">
            <summary>
            Unified input manager that handles both touch and mouse input,
            with platform-specific optimizations for mobile devices.
            </summary>
        </member>
        <member name="P:Client.Main.Input.InputManager.IsTouchPrimary">
            <summary>
            Gets whether touch input is currently being used as the primary input method.
            </summary>
        </member>
        <member name="P:Client.Main.Input.InputManager.ShouldPrioritizeTouch">
            <summary>
            Gets whether the current platform should prioritize touch input.
            </summary>
        </member>
        <member name="M:Client.Main.Input.InputManager.UpdateInputState(Microsoft.Xna.Framework.Input.Touch.TouchCollection,Microsoft.Xna.Framework.Input.MouseState,Microsoft.Xna.Framework.Input.Touch.TouchCollection,Microsoft.Xna.Framework.Input.MouseState)">
            <summary>
            Updates the input state and determines the primary input method.
            </summary>
            <param name="touchState">Current touch state</param>
            <param name="mouseState">Current mouse state</param>
            <param name="prevTouchState">Previous touch state</param>
            <param name="prevMouseState">Previous mouse state</param>
        </member>
        <member name="M:Client.Main.Input.InputManager.ConvertTouchToMouseState(Microsoft.Xna.Framework.Input.Touch.TouchCollection,Microsoft.Xna.Framework.Input.Touch.TouchCollection,Microsoft.Xna.Framework.Input.MouseState)">
            <summary>
            Converts touch input to a mouse state for compatibility with existing mouse-based code.
            </summary>
            <param name="touchState">Current touch state</param>
            <param name="prevTouchState">Previous touch state</param>
            <param name="fallbackMouseState">Mouse state to use as fallback</param>
            <returns>A mouse state representing the touch input</returns>
        </member>
        <member name="M:Client.Main.Input.InputManager.GetEffectiveMouseState(Microsoft.Xna.Framework.Input.MouseState,Microsoft.Xna.Framework.Input.Touch.TouchCollection,Microsoft.Xna.Framework.Input.Touch.TouchCollection)">
            <summary>
            Gets the effective mouse state, considering touch input conversion.
            </summary>
            <param name="mouseState">Raw mouse state</param>
            <param name="touchState">Current touch state</param>
            <param name="prevTouchState">Previous touch state</param>
            <returns>The effective mouse state to use</returns>
        </member>
        <member name="M:Client.Main.Input.InputManager.IsClickGesture(Microsoft.Xna.Framework.Input.MouseState,Microsoft.Xna.Framework.Input.MouseState,Microsoft.Xna.Framework.Input.Touch.TouchCollection,Microsoft.Xna.Framework.Input.Touch.TouchCollection)">
            <summary>
            Checks if a click/tap gesture has occurred.
            </summary>
            <param name="currentState">Current mouse/touch state</param>
            <param name="previousState">Previous mouse/touch state</param>
            <param name="touchState">Current touch state</param>
            <param name="prevTouchState">Previous touch state</param>
            <returns>True if a click/tap gesture was detected</returns>
        </member>
        <member name="M:Client.Main.Input.InputManager.GetInputPosition(Microsoft.Xna.Framework.Input.MouseState,Microsoft.Xna.Framework.Input.Touch.TouchCollection)">
            <summary>
            Gets the current input position (mouse or primary touch).
            </summary>
            <param name="mouseState">Current mouse state</param>
            <param name="touchState">Current touch state</param>
            <returns>The current input position</returns>
        </member>
        <member name="M:Client.Main.Models.DirectionExtensions.GetDirectionFromAngle(System.Single)">
            <summary>
            Maps a given angle (in radians) to the nearest Direction value.
            </summary>
            <param name="angle">Rotation angle in radians.</param>
            <returns>Nearest Direction enum value.</returns>
        </member>
        <member name="M:Client.Main.Models.DirectionExtensions.GetDirectionFromTileDifference(Microsoft.Xna.Framework.Vector2,Microsoft.Xna.Framework.Vector2)">
            <summary>
            Calculates the game direction from a 'from' tile to a 'to' tile.
            </summary>
            <param name="fromTile">The starting tile position.</param>
            <param name="toTile">The target tile position.</param>
            <returns>The Direction enum value.</returns>
        </member>
        <member name="M:Client.Main.Models.DirectionExtensions.GetDirectionFromMovementDelta(System.Int32,System.Int32)">
            <summary>
            Determines the Direction enum based on a movement delta (targetTile - currentTile).
            This logic mirrors WalkerObject.OnLocationChanged's implicit mapping.
            Assumes standard tile map coordinates: +X is East (right), +Y is South (down).
            </summary>
            <param name="dx">Change in X tile coordinate (target.X - player.X).</param>
            <param name="dy">Change in Y tile coordinate (target.Y - player.Y).</param>
            <returns>The Direction enum value for visual facing.</returns>
        </member>
        <member name="M:Client.Main.MuGame.ScheduleOnMainThread(System.Action)">
            <summary>
            Schedules an action to be executed on the main game thread during the next Update cycle.
            </summary>
            <param name="action">The action to execute.</param>
        </member>
        <member name="M:Client.Main.MuGame.DisposeNetworkSafely">
            <summary>
            Synchronously disposes <see cref="P:Client.Main.MuGame.Network"/> exactly once,
            swallowing <see cref="T:System.ObjectDisposedException"/> which occurs,
            if the CancellationTokenSource inside NetworkManager was already disposed.
            </summary>
        </member>
        <member name="M:Client.Main.MuGame.OnGameExiting(System.Object,System.EventArgs)">
            <summary>
            Handles the Game.Exiting event to ensure proper cleanup and process termination.
            </summary>
        </member>
        <member name="T:Client.Main.Networking.ConnectionManager">
            <summary>
            Manages TCP network connections, including establishing, maintaining, and disconnecting connections.
            Handles encryption and decryption pipelines using SimpleModulus and Xor32 algorithms.
            Supports sequential connections to different endpoints (e.g., Connect Server then Game Server).
            Implements IAsyncDisposable for proper resource management.
            </summary>
        </member>
        <member name="P:Client.Main.Networking.ConnectionManager.Connection">
            <summary>
            Gets the current network connection. Throws an exception if the connection is not initialized or has been disconnected.
            </summary>
        </member>
        <member name="P:Client.Main.Networking.ConnectionManager.IsConnected">
            <summary>
            Gets a value indicating whether the current network connection is established and active.
            Checks both the connection object and its underlying Connected property.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.ConnectionManager.#ctor(Microsoft.Extensions.Logging.ILoggerFactory,MUnique.OpenMU.Network.SimpleModulus.SimpleModulusKeys,MUnique.OpenMU.Network.SimpleModulus.SimpleModulusKeys)">
            <summary>
            Initializes a new instance of the <see cref="T:Client.Main.Networking.ConnectionManager"/> class.
            </summary>
            <param name="loggerFactory">The logger factory used to create loggers.</param>
            <param name="encryptKeys">The SimpleModulus keys for encryption.</param>
            <param name="decryptKeys">The SimpleModulus keys for decryption.</param>
        </member>
        <member name="M:Client.Main.Networking.ConnectionManager.ConnectAsync(System.String,System.Int32,System.Boolean,System.Threading.CancellationToken)">
            <summary>
            Establishes a TCP connection to the specified host and port.
            Configures the packet processing pipeline, including optional encryption.
            Creates connection resources but DOES NOT start the receiving loop automatically.
            Uses local variables for new resources and assigns them to class fields only upon full success.
            </summary>
            <param name="host">The host name or IP address of the server.</param>
            <param name="port">The port number of the server.</param>
            <param name="useEncryption">True to enable encryption (SimpleModulus and Xor32), false for a raw, unencrypted connection.</param>
            <param name="cancellationToken">A cancellation token that can be used to cancel the connection attempt.</param>
            <returns>True if the connection infrastructure was successfully established; otherwise, false.</returns>
        </member>
        <member name="M:Client.Main.Networking.ConnectionManager.StartReceiving(System.Threading.CancellationToken)">
            <summary>
            Starts the background packet receiving loop for the established connection.
            Should only be called after ConnectAsync returns true.
            </summary>
            <param name="externalCancellationToken">Optional external cancellation token to link with the internal CTS.</param>
        </member>
        <member name="M:Client.Main.Networking.ConnectionManager.DisconnectAsync">
            <summary>
            Gracefully disconnects the current active network connection.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.ConnectionManager.CleanupCurrentConnectionAsync">
            <summary>
            Cleans up resources associated with the CURRENT connection fields.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.ConnectionManager.CleanupTemporaryResourcesAsync(Pipelines.Sockets.Unofficial.SocketConnection,MUnique.OpenMU.Network.IConnection,System.Threading.CancellationTokenSource)">
            <summary>
            Helper method to clean up resources created temporarily during a failed ConnectAsync attempt.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.ConnectionManager.DisposeAsync">
            <summary>
            Asynchronously disposes of the ConnectionManager, ensuring all resources are released.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.NetworkManager.SendPublicChatMessageAsync(System.String)">
            <summary>
            Sends a public chat message (including party, guild, gens with prefixes) to the server.
            </summary>
            <param name="message">The message content, potentially including prefixes like ~, @, $.</param>
        </member>
        <member name="M:Client.Main.Networking.NetworkManager.SendWhisperMessageAsync(System.String,System.String)">
            <summary>
            Sends a whisper message to the specified receiver.
            </summary>
            <param name="receiver">The name of the character to receive the whisper.</param>
            <param name="message">The message content.</param>
        </member>
        <member name="M:Client.Main.Networking.NetworkManager.GetCachedServerList">
            <summary>
            Gets a read-only view of the currently cached server list.
            </summary>
            <returns>A read-only list of ServerInfo objects.</returns>
        </member>
        <member name="M:Client.Main.Networking.NetworkManager.SendWarpRequestAsync(System.UInt16,System.UInt32)">
            <summary>
            Sends a warp command request to the server.
            </summary>
            <param name="warpInfoIndex">The index of the warp destination.</param>
            <param name="commandKey">Optional command key, if required by the server (default is 0).</param>
        </member>
        <member name="M:Client.Main.Networking.NetworkManager.SendLoginRequestAsync(System.String,System.String)">
            <summary>
            Sends a login request using the provided username and password.
            </summary>
            <param name="username">Username from UI.</param>
            <param name="password">Password from UI.</param>
        </member>
        <member name="T:Client.Main.Networking.PacketHandling.Handlers.CharacterDataHandler">
            <summary>
            Handles packets related to character stats, level, status, and skills.
            </summary>
        </member>
        <member name="T:Client.Main.Networking.PacketHandling.Handlers.ChatMessageHandler">
            <summary>
            Implements IGamePacketHandler to process server and chat messages.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.Handlers.ChatMessageHandler.TakePendingServerMessages">
            <summary>
            Retrieves and clears any queued server messages for processing when the scene is ready.
            </summary>
        </member>
        <member name="T:Client.Main.Networking.PacketHandling.Handlers.ConnectServerHandler">
            <summary>
            Processes packets from the connect server, including handshake, server list, and connection info.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.Handlers.ConnectServerHandler.HandleHelloAsync(System.Memory{System.Byte})">
            <summary>
            Handles the initial Hello packet from the connect server.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.Handlers.ConnectServerHandler.HandleServerListResponseAsync(System.Memory{System.Byte})">
            <summary>
            Handles the response containing the list of available game servers.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.Handlers.ConnectServerHandler.HandleConnectionInfoResponseAsync(System.Memory{System.Byte})">
            <summary>
            Handles the response containing connection details for the selected game server.
            </summary>
        </member>
        <member name="T:Client.Main.Networking.PacketHandling.Handlers.IGamePacketHandler">
            <summary>
            Defines the interface for a class that can handle specific game packets.
            Packet registration typically happens via PacketHandlerAttribute on methods.
            </summary>
        </member>
        <member name="T:Client.Main.Networking.PacketHandling.Handlers.InventoryHandler">
            <summary>
            Handles packets related to inventory, items, and money updates.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.Handlers.InventoryHandler.UpdateInventoryFromPacket(System.ReadOnlySpan{System.Byte})">
            <summary>
            Parses the inventory packet and updates CharacterState accordingly.
            </summary>
        </member>
        <member name="T:Client.Main.Networking.PacketHandling.Handlers.MiscGamePacketHandler">
            <summary>
            Handles miscellaneous game packets such as login, character listing, weather, quests, and messenger initialization.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.Handlers.MiscGamePacketHandler.MapClassValueToEnum(System.Int32)">
            <summary>
            Maps a raw 5-bit class value to the CharacterClassNumber enum.
            Update mappings to match your server's definitions.
            </summary>
        </member>
        <member name="T:Client.Main.Networking.PacketHandling.Handlers.ScopeHandler">
            <summary>
            Handles packets related to objects entering or leaving scope, moving, and dying.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.Handlers.ScopeHandler.TakePendingPlayers">
            <summary>
            Retrieves and clears pending player spawns.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.Handlers.ScopeHandler.TakePendingNpcsMonsters">
            <summary>
            Retrieves and clears pending NPC and monster spawns.
            </summary>
        </member>
        <member name="T:Client.Main.Networking.PacketHandling.PacketBuilder">
            <summary>
            Builds outgoing network packets for game and connect server communication.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildLoginPacket(System.Buffers.IBufferWriter{System.Byte},System.String,System.String,System.Byte[],System.Byte[],System.Byte[])">
            <summary>
            Builds a login packet using the long-password format.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildRequestCharacterListPacket(System.Buffers.IBufferWriter{System.Byte})">
            <summary>
            Builds a packet requesting the character list.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildPublicChatMessagePacket(System.Buffers.IBufferWriter{System.Byte},System.String,System.String)">
            <summary>
            Builds a packet for sending a public chat message.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildWhisperMessagePacket(System.Buffers.IBufferWriter{System.Byte},System.String,System.String)">
            <summary>
            Builds a whisper (private chat) packet.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildSelectCharacterPacket(System.Buffers.IBufferWriter{System.Byte},System.String)">
            <summary>
            Builds a packet to select a character by name.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildInstantMoveRequestPacket(System.Buffers.IBufferWriter{System.Byte},System.Byte,System.Byte)">
            <summary>
            Builds a packet for an instant move (teleport) request.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildWalkRequestPacket(System.Buffers.IBufferWriter{System.Byte},System.Byte,System.Byte,System.Byte[])">
            <summary>
            Builds a walk request packet with a sequence of direction steps.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildPickupItemRequestPacket(System.Buffers.IBufferWriter{System.Byte},System.UInt16,Client.Main.Core.Client.TargetProtocolVersion)">
            <summary>
            Builds a packet requesting to pick up an item by ID.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildAnimationRequestPacket(System.Buffers.IBufferWriter{System.Byte},System.Byte,System.Byte)">
            <summary>
            Builds a packet to request an animation or rotation.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildHitRequestPacket(System.Buffers.IBufferWriter{System.Byte},System.UInt16,System.Byte,System.Byte)">
            <summary>
            Builds a hit request packet for a basic attack.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildIncreaseCharacterStatPointPacket(System.Buffers.IBufferWriter{System.Byte},MUnique.OpenMU.Network.Packets.CharacterStatAttribute)">
            <summary>
            Builds a packet to request increasing a character's stat point.
            </summary>
            <param name="writer">The buffer writer to write the packet to.</param>
            <param name="attribute">The attribute to increase.</param>
            <returns>The length of the built packet.</returns>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildServerListRequestPacket(System.Buffers.IBufferWriter{System.Byte})">
            <summary>
            Builds a request packet for the game server list from the connect server.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.BuildServerInfoRequestPacket(System.Buffers.IBufferWriter{System.Byte},System.UInt16)">
            <summary>
            Builds a request packet for connection info of a specific game server.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketBuilder.EncryptXor3(System.Span{System.Byte},System.Byte[])">
            <summary>
            Applies XOR-3 encryption to the provided span in place.
            </summary>
        </member>
        <member name="T:Client.Main.Networking.PacketHandling.PacketRouter">
            <summary>
            Routes incoming network packets to the appropriate handlers
            for Connect Server or Game Server connections.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketRouter.SetRoutingMode(System.Boolean)">
            <summary>
            Switches between Connect Server and Game Server packet routing.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketRouter.RoutePacketAsync(System.Buffers.ReadOnlySequence{System.Byte})">
            <summary>
            Routes an incoming packet based on current routing mode.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.PacketHandling.PacketRouter.OnDisconnected">
            <summary>
            Handles disconnection events.
            </summary>
        </member>
        <member name="T:Client.Main.Networking.Services.CharacterService">
            <summary>
            Manages sending character‐related packets to the game server,
            including character list requests, character selection, movement, and animations.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.Services.CharacterService.RequestCharacterListAsync">
            <summary>
            Requests the list of characters for the current account.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.Services.CharacterService.SelectCharacterAsync(System.String)">
            <summary>
            Selects the specified character on the game server.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.Services.CharacterService.SendInstantMoveRequestAsync(System.Byte,System.Byte)">
            <summary>
            Sends an instant move (teleport) request to the given coordinates.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.Services.CharacterService.SendAnimationRequestAsync(System.Byte,System.Byte)">
            <summary>
            Sends an animation request with the specified rotation and animation number.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.Services.CharacterService.SendWalkRequestAsync(System.Byte,System.Byte,System.Byte[])">
            <summary>
            Sends a walk request along a path of direction steps.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.Services.CharacterService.SendHitRequestAsync(System.UInt16,System.Byte,System.Byte)">
            <summary>
            Sends a hit request packet to the server.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.Services.CharacterService.SendIncreaseCharacterStatPointRequestAsync(MUnique.OpenMU.Network.Packets.CharacterStatAttribute)">
            <summary>
            Sends a request to increase a specific character stat attribute.
            </summary>
            <param name="attribute">The attribute to be increased.</param>
        </member>
        <member name="M:Client.Main.Networking.Services.CharacterService.SendPickupItemRequestAsync(System.UInt16,Client.Main.Core.Client.TargetProtocolVersion)">
            <summary>
            Sends a request to pick up a dropped item or money by its network ID.
            </summary>
        </member>
        <member name="T:Client.Main.Networking.Services.ConnectServerService">
            <summary>
            Handles communication with the Connect Server:
            requesting server lists and game server connection info.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.Services.ConnectServerService.RequestServerListAsync">
            <summary>
            Requests the list of available game servers from the Connect Server.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.Services.ConnectServerService.RequestConnectionInfoAsync(System.UInt16)">
            <summary>
            Requests connection information (IP and port) for the specified game server.
            </summary>
            <param name="serverId">The ID of the target game server.</param>
        </member>
        <member name="T:Client.Main.Networking.Services.LoginService">
            <summary>
            Handles login requests: encrypts credentials and sends the login packet
            including client version and serial information.
            </summary>
        </member>
        <member name="M:Client.Main.Networking.Services.LoginService.SendLoginRequestAsync(System.String,System.String)">
            <summary>
            Sends a login request using the specified username and password.
            Credentials are encrypted with XOR3 before sending.
            </summary>
            <param name="username">The account username.</param>
            <param name="password">The account password.</param>
        </member>
        <member name="T:Client.Main.Objects.DroppedItemObject">
            <summary>
            Dropped item or Zen; the label disappears only when the server
            removes the object from scope.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.HumanoidObject.SetBodyPartsAsync(System.String,System.String,System.String,System.String,System.String,System.String,System.Int32)">
            <summary>
            Loads the models for all body parts based on a specified path prefix, part prefixes, and a file suffix.
            Example: ("Npc/", "FemaleHead", "FemaleUpper", ..., 2) -> "Data/Npc/FemaleHead02.bmd"
            </summary>
        </member>
        <member name="P:Client.Main.Objects.Monsters.MonsterObject.DisplayName">
            <summary>
            Gets the monster's display name defined by <see cref="T:NpcInfoAttribute"/>.
            </summary>
        </member>
        <member name="P:Client.Main.Objects.Monsters.MonsterObject.IsDead">
            <summary>
            Indicates whether the monster is in its death fade stage.
            While true the monster should no longer be considered alive.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.Monsters.MonsterObject.OnIdle">
            <summary>
            Called when the monster enters idle state.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.Monsters.MonsterObject.OnStartWalk">
            <summary>
            Called when the monster starts walking.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.Monsters.MonsterObject.OnPerformAttack(System.Int32)">
            <summary>
            Called when the monster performs an attack.
            </summary>
            <param name="attackType">Attack variation index.</param>
        </member>
        <member name="M:Client.Main.Objects.Monsters.MonsterObject.OnReceiveDamage">
            <summary>
            Called when the monster receives damage.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.Monsters.MonsterObject.OnDeathAnimationStart">
            <summary>
            Called when the monster’s death animation starts.
            </summary>
        </member>
        <member name="T:Client.Main.Objects.NPCS.CompositeNPCObject">
            <summary>
            Base class for humanoid NPCs composed of multiple parts (head, armor, etc.),
            inheriting from HumanoidObject. Implements the clickable NPC pattern.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.Player.PlayerEquipment.GetWeaponTypeForClass(MUnique.OpenMU.Network.Packets.CharacterClassNumber)">
            <summary>
            Determines weapon type based on character class (temporary until we have real equipment)
            </summary>
        </member>
        <member name="M:Client.Main.Objects.Player.PlayerObject.GetAttackAnimation">
            <summary>
            Gets the appropriate attack animation based on equipped weapon
            </summary>
        </member>
        <member name="M:Client.Main.Objects.WalkerObject.Animation(Microsoft.Xna.Framework.GameTime)">
            <summary>
            Advances the current animation and builds bone matrices for this frame.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.WalkerObject.PlayAction(System.UInt16,System.Boolean)">
            <summary>
            Plays the specified action using the centralized animation controller.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.WorldObject.DrawHoverName">
            <summary>
            Draws the object's <see cref="P:Client.Main.Objects.WorldObject.DisplayName"/> above it when hovered.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.Worlds.Devias.HouseWallObject.IsPlayerUnderRoof(Client.Main.Controls.WalkableWorldControl)">
            <summary>
            Determines if the player is inside the building (under the roof).
            </summary>
        </member>
        <member name="M:Client.Main.Objects.Worlds.Login.StatueTorchObject.UpdateDynamicLight(System.Single)">
            <summary>
            Updates the position and intensity of the dynamic light source.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.Worlds.Login.StatueTorchObject.CalculateBaseLuminosity(System.Single)">
            <summary>
            Calculates the flickering brightness of the torch flame based on time.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.Worlds.Lorencia.CandleObject.UpdateDynamicLight(System.Single)">
            <summary>
            Updates the position and intensity of the dynamic light source.
            </summary>
        </member>
        <member name="M:Client.Main.Objects.Worlds.Lorencia.CandleObject.CalculateBaseLuminosity(System.Single)">
            <summary>
            Calculates the flickering brightness of the candle flame based on time.
            </summary>
        </member>
        <member name="M:Client.Main.Scenes.GameScene.PreloadUITextures">
            <summary>
            Preloads textures for UI controls to avoid stalls when opening them later.
            </summary>
        </member>
        <member name="T:Client.Main.Helpers.SpriteBatchScope">
            <summary>
            Manages nested SpriteBatch.Begin/End calls, preserving and restoring all parameters.
            </summary>
        </member>
        <member name="P:Client.Main.Helpers.SpriteBatchScope.BatchIsBegun">
            <summary>
            True if there is currently an open SpriteBatch (anywhere in the stack).
            </summary>
        </member>
        <member name="T:Client.Main.Helpers.SpriteBatchScope.SavedState">
            <summary>
            Holds all parameters necessary to Begin/End a SpriteBatch with the same settings.
            </summary>
        </member>
        <member name="T:Client.Main.Resource">
            <summary>
            Android Resource Designer class.
            Exposes the Android Resource designer assembly into the project Namespace.
            </summary>
        </member>
    </members>
</doc>
