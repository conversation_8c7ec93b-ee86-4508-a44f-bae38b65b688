MMicrosoft.Extensions.DependencyInjection.VerifyOpenGenericServiceTrimmabilitytrue7System.ComponentModel.DefaultValueAttribute.IsSupportedtrue'System.Diagnostics.Debugger.IsSupportedfalse,System.Diagnostics.Metrics.Meter.IsSupportedfalse2System.Diagnostics.Tracing.EventSource.IsSupportedfalseSystem.Globalization.Invariantfalse)System.Net.Http.EnableActivityPropagationfalse$System.Net.Http.UseNativeHttpHandlertrue6System.Reflection.Metadata.MetadataUpdater.IsSupportedfalse<System.Runtime.InteropServices.BuiltInComInterop.IsSupportedfalseESystem.Runtime.Serialization.EnableUnsafeBinaryFormatterSerializationfalse&System.StartupHookProvider.IsSupportedfalse-System.Text.Encoding.EnableUnsafeUTF7Encodingfalse<System.Text.Json.JsonSerializer.IsReflectionEnabledByDefaulttrue.Xamarin.Android.Net.UseNegotiateAuthenticationfalse/Switch.System.Reflection.ForceInterpretedInvoketrue=Microsoft.Extensions.DependencyInjection.DisableDynamicEnginetrue