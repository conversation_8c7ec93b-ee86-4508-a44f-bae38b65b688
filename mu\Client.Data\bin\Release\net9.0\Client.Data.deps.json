{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"Client.Data/1.0.0": {"dependencies": {"BCnEncoder.Net": "2.2.0", "BouncyCastle.NetCore": "2.2.1", "Delizious.Ini": "1.17.0", "LEA.NET": "1.0.1", "Microsoft.DotNet.ILCompiler": "9.0.6", "Microsoft.NET.ILLink.Tasks": "9.0.6", "cryptopp": "*******", "SixLabors.ImageSharp": "3.1.7"}, "runtime": {"Client.Data.dll": {}}}, "BCnEncoder.Net/2.2.0": {"dependencies": {"CommunityToolkit.HighPerformance": "8.4.0"}, "runtime": {"lib/netstandard2.1/BCnEncoder.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "BouncyCastle.NetCore/2.2.1": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "0.0.0.0", "fileVersion": "0.0.0.0"}}}, "CommunityToolkit.HighPerformance/8.4.0": {"runtime": {"lib/net8.0/CommunityToolkit.HighPerformance.dll": {"assemblyVersion": "8.4.0.0", "fileVersion": "8.4.0.1"}}}, "cryptopp/*******": {}, "Delizious.Ini/1.17.0": {"dependencies": {"ini-parser-netstandard": "2.5.2"}, "runtime": {"lib/netstandard2.0/Delizious.Ini.dll": {"assemblyVersion": "1.17.0.0", "fileVersion": "1.17.0.0"}}}, "ini-parser-netstandard/2.5.2": {"runtime": {"lib/netstandard2.0/INIFileParser.dll": {"assemblyVersion": "2.5.2.0", "fileVersion": "2.5.2.0"}}}, "LEA.NET/1.0.1": {"runtime": {"lib/net7.0/LEA.NET.dll": {"assemblyVersion": "1.0.1.0", "fileVersion": "1.0.1.0"}}}, "Microsoft.DotNet.ILCompiler/9.0.6": {}, "Microsoft.NET.ILLink.Tasks/9.0.6": {}, "SixLabors.ImageSharp/3.1.7": {"runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.1.7.0"}}}}}, "libraries": {"Client.Data/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "BCnEncoder.Net/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-bJg23yquVTodLWYBCDu28Dk82Coi138ZnjFlWI4sqaOtjRIvftAZHaQHbm2SdCWUShcG9sEZ4BdOnePY9qB7tA==", "path": "bcnencoder.net/2.2.0", "hashPath": "bcnencoder.net.2.2.0.nupkg.sha512"}, "BouncyCastle.NetCore/2.2.1": {"type": "package", "serviceable": true, "sha512": "sha512-yfWn8JYPc4rkeM2kcsCqFVFOvwCuuQvIieGtQWcjoWxOioeznXQB3M/GmHgbCWbJjc8ycrwGhZaZPiasifYi4A==", "path": "bouncycastle.netcore/2.2.1", "hashPath": "bouncycastle.netcore.2.2.1.nupkg.sha512"}, "CommunityToolkit.HighPerformance/8.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-flxspiBs0G/0GMp7IK2J2ijV9bTG6hEwFc/z6ekHqB6nwRJ4Ry2yLdx+TkbCUYFCl4XhABkAwomeKbT6zM2Zlg==", "path": "communitytoolkit.highperformance/8.4.0", "hashPath": "communitytoolkit.highperformance.8.4.0.nupkg.sha512"}, "cryptopp/*******": {"type": "package", "serviceable": true, "sha512": "sha512-zU/hIppFiZkZMU+DRqR4Lr2EckFwD+c2GCLhK8yUDrBRf+EWkGs683CRlWpmHav7CvIPsVbIOBbl6VnJ62sHSg==", "path": "cryptopp/*******", "hashPath": "cryptopp.*******.nupkg.sha512"}, "Delizious.Ini/1.17.0": {"type": "package", "serviceable": true, "sha512": "sha512-2aLGQWr3nj2bzuNWivo5612ACoWd/S7YdbkVSMG/CRSIlaOWU+ZVZXndqt/a/MogOPFRlxcIBzrLvSE/GsQrrw==", "path": "delizious.ini/1.17.0", "hashPath": "delizious.ini.1.17.0.nupkg.sha512"}, "ini-parser-netstandard/2.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-rTyGzgT/a+mD2HxopOGpb/yWWhu5TTPgF59XUlHjhlNv7dbt9DME/qozIR7HreVAVpNnZSqP9A8TeViK5s949g==", "path": "ini-parser-netstandard/2.5.2", "hashPath": "ini-parser-netstandard.2.5.2.nupkg.sha512"}, "LEA.NET/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Tfo6otn/s8/4zOOnBjO86pswCkQ1YxoWauCugok3Wf6OceHqzig1kgkPDYUMJM0E7zWRpavrXpt9Zap6tFg+lw==", "path": "lea.net/1.0.1", "hashPath": "lea.net.1.0.1.nupkg.sha512"}, "Microsoft.DotNet.ILCompiler/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-O7Row/kOgEA3PXpeCVKpDJ9ooAoJDoKoxpZC86voOAwV2NA1jTbQ0Nn0VCuoTT/IvU0T7YNlPldAS0mOrppXmw==", "path": "microsoft.dotnet.ilcompiler/9.0.6", "hashPath": "microsoft.dotnet.ilcompiler.9.0.6.nupkg.sha512"}, "Microsoft.NET.ILLink.Tasks/9.0.6": {"type": "package", "serviceable": true, "sha512": "sha512-TXy3SbJzGXQbxxIxCjdrp8bwEyTDImyYNpTpd6v7P3JL2Y7dno8EYG7dPezfYTa5SoWKdhbH9cbnwHHs3BR5gA==", "path": "microsoft.net.illink.tasks/9.0.6", "hashPath": "microsoft.net.illink.tasks.9.0.6.nupkg.sha512"}, "SixLabors.ImageSharp/3.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-9fIOOAsyLFid6qKypM2Iy0Z3Q9yoanV8VoYAHtI2sYGMNKzhvRTjgFDHonIiVe+ANtxIxM6SuqUzj0r91nItpA==", "path": "sixlabors.imagesharp/3.1.7", "hashPath": "sixlabors.imagesharp.3.1.7.nupkg.sha512"}}}