<?xml version="1.0" encoding="utf-8"?>
<!--
    This code was generated by a tool.
    It was generated from C:\Users\<USER>\Downloads\muonline-main\mu\MuAndroid\AndroidManifest.xml
    Changes to this file may cause incorrect behavior and will be lost if
    the contents are regenerated.
    -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="MuAndroid.MuAndroid" android:versionCode="1" android:versionName="1.0">
  <uses-sdk android:minSdkVersion="23" android:targetSdkVersion="35" />
  <uses-feature android:glEsVersion="0x00020000" android:required="true" />
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <application android:label="MUV1.0.3" android:icon="@drawable/icon" android:usesCleartextTraffic="true" android:hardwareAccelerated="true" android:largeHeap="true" android:name="android.app.Application" android:allowBackup="true" android:extractNativeLibs="true">
    <activity android:alwaysRetainTaskState="true" android:configChanges="keyboard|keyboardHidden|orientation|screenSize" android:icon="@drawable/icon" android:label="@string/app_name" android:launchMode="singleInstance" android:screenOrientation="landscape" android:name="crc64ee52d6f9ca8a8df3.MainActivity" android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
    </activity>
    <provider android:name="mono.MonoRuntimeProvider" android:exported="false" android:initOrder="**********" android:authorities="MuAndroid.MuAndroid.mono.MonoRuntimeProvider.__mono_init__" />
  </application>
</manifest>